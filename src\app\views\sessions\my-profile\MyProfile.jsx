import { SuccessToastConfig, ErrorToastConfig } from "app/utils/ToastConstants";
import { useEffect, useRef, useState } from "react";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { But<PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import { useNavigate } from "react-router-dom";
import { validateEmail } from "app/utils/utility.service";
import { Loading } from "app/components";
import { getSingleUser, updateUser } from "./my-profile.service";
import {
  getSessionUserInformation,
  getConvertedFileName,
} from "app/utils/utility.service";
import { FileUpload } from "primereact/fileupload";
import { Password } from "primereact/password";
import { Calendar } from "primereact/calendar";
import moment from "moment";
import { RadioButton } from "primereact/radiobutton";
import { InputTextarea } from "primereact/inputtextarea";
import { setSessionUserInformation } from "app/utils/utility.service";
import { SYSTEM_OF_MEDICINE_LIST } from "app/utils/Constants";
import { QUALIFICATION_LIST } from "app/utils/Constants";
import { getDateObject } from "app/utils/utility.service";

const MyProfile = () => {
  const [values, setValues] = useState(null);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useRef();
  const sessionValues = getSessionUserInformation();
  const showSuccess = (message) => {
    toast.current.show(SuccessToastConfig(message));
  };

  const recordId = sessionValues.id;

  const showError = (message) => {
    toast.current.show(ErrorToastConfig(message ? message : "Error"));
  };

  const validateInput = () => {
    console.log(
      !values.email,
      !values.firstname,
      !values.mobile_number,
      !values.qualification,
      !values.certificate_status,
      !values.date_of_birth,
      !values.system_of_medicine,
      !values.awarding_body,
      !values.permanent_address
    );
    if (
      !values.email ||
      !values.firstname ||
      !values.mobile_number ||
      !values.qualification ||
      !values.certificate_status ||
      !values.date_of_birth ||
      !values.system_of_medicine ||
      !values.awarding_body ||
      !values.permanent_address
    ) {
      return false;
    }

    if (!validateEmail(values.email)) {
      return false;
    }

    // if (isNaN(values.state_registration_number)) {
    //     return false;
    // }
    if (isNaN(values.mobile_number)) {
      return false;
    } else {
      if (values.mobile_number.length !== 10) {
        return false;
      }
    }
    // if (isNaN(values.state_registration_number)) {
    //     return false;
    // }

    // if (values?.certificate_status === "Old Certificate") {
    //     if (!values?.central_registration_number) {
    //         return false;
    //     } else {
    //         if (isNaN(values.central_registration_number)) {
    //             return false;
    //         }
    //     }
    // }

    // if (!values?.dateOfBirthImage?.name) {
    //     return false;
    // }
    // if (!values?.addressProofImage?.name) {
    //     return false;
    // }
    // if (!values?.aadharProof?.name) {
    //     return false;
    // }
    return true;
  };

  function processText(inputText) {
    // Split the text by commas
    var parts = inputText.split(",");

    // Remove leading and trailing spaces from each part and join with a comma and space
    var resultText = parts
      .map(function (part) {
        return part.trim();
      })
      .join(", ");

    return resultText;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitted(true);
    if (!validateInput()) {
      return false;
    }
    setIsLoading(true);
    if (recordId) {
      let formData = new FormData();
      // formData.append("email", values?.email?.trim());
      // if(values.password) {
      //  formData.append("password", values?.password);
      // }
      // formData.append("firstName", values?.firstName);
      // formData.append("lastName", values?.lastName);
      // formData.append("phoneNumber", values?.phoneNumber);
      // formData.append("city", values?.city);
      // formData.append("state", values?.state);
      // formData.append("zipcode", values?.zipcode);
      // formData.append("address", values?.address);
      //     if (!!values?.aadharProof?.name) {
      //     formData.append(
      //         "userfiles",
      //         values.aadharProof,
      //         `AADHARPROOF_${values?.aadharProof?.name}`
      //     );
      // } else {
      //     alert("Please upload Aadhar Proof PDF");
      //     return;
      // }
      if (values.profileImageName) {
        console.log("inside");
        const convertedFileName = getConvertedFileName(
          values?.profileImageName
        );
        formData.append("profileImagePath", `${convertedFileName}`);
        formData.append("images", values?.profileImage, `${convertedFileName}`);
      }
      if (values.signatureImageName) {
        console.log("inside");
        const convertedFileName = getConvertedFileName(
          values?.signatureImageName
        );
        formData.append("signatureImagePath", `${convertedFileName}`);
        formData.append(
          "images",
          values?.signatureImage,
          `${convertedFileName}`
        );
      }
      delete values?.profileImage;
      delete values?.profileImagePath;
      delete values?.signatureImage;
      delete values?.signatureImagePath;
      values.documentUploaded = !values.documentUploaded
        ? 0
        : values.documentUploaded;
      values.profileImageUploaded = !values.profileImageUploaded
        ? 0
        : values.profileImageUploaded;

      values.contact_address = processText(values.contact_address);
      values.permanent_address = processText(values.permanent_address);
      // for (const key of Object.keys(values)) {
      //     if (values[key] !== null && values[key]) {
      //         formData.append(key, values[key]);
      //     }
      // }
      const record = await updateUser(recordId, values);
      if (record?.status === false) {
        setIsLoading(false);
        showError(record?.message);
      } else {
        showSuccess("Profile updated successfully");
        // insert settings value
        setValues(null);
        setSessionUserInformation(record?.data);
        setValues(record?.data);
        setIsLoading(false);
      }
    }
  };

  const myUploader = (event, name) => {
    console.log(event.files[0]);
    setValues({
      ...values,
      [name]: event.files[0],
      [`${name}Name`]: event.files[0].name,
    });
  };

  useEffect(() => {
    const fetchUser = async () => {
      setIsLoading(true);
      let record = await getSingleUser(recordId);
      setValues(record);
      setIsLoading(false);
    };
    if (recordId) {
      fetchUser();
    }
  }, []);

  const handleInputChange = async (e) => {
    const { name, value } = e.target;
    setValues({
      ...values,
      [name]: value,
    });
  };

  const cardFooter = (
    <div className="flex justify-content-between">
      <span className="ml-3">
        {/* <Link to="/clients" style={{textDecoration:"none"}}> */}

        {/* </Link> */}
        <Button
          // onClick={handleSubmit}
          // disabled={!values?.profileImagePath}
          label={!recordId ? "Create Account" : "Update Account"}
          className="p-button-raised p-button-warning p-button-lg"
        />
      </span>
      {/* <span className="ml-3">
            <Button
                // onClick={handleSubmit}
                // disabled={!values?.profileImagePath}
                label={"Next"}
                className="p-button-raised p-button p-button-lg"
            />
        </span> */}
    </div>
  );

  return (
    <div className="center-align">
      {isLoading && <Loading />}
      <Toast ref={toast} className="ToastMessage" />
      <div className="RegisterForm">
        <form method="post" onSubmit={handleSubmit}>
          <Card
            title={`My Profile | Selected Category : ${values?.category}`}
            // subTitle="You can only register if you are citizen of India"
            footer={cardFooter}
          >
            {values?.certificate_status === "Old Certificate" && (
              <h2 className="ml-3">
                <strong>Note:</strong> The Central Registration certificate will
                be issued only on receipt of the CCRYN certificate
              </h2>
            )}
            <div className="grid">
              <div className="col-12 md:col-3 lg:col content pr-4 pl-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      autoFocus
                      className="vw-input"
                      autoComplete="off"
                      disabled={true}
                      name="firstname"
                      value={"Dr"}
                    />
                    <label>Prefix *</label>
                  </span>
                </div>
              </div>
              <div className="col-12 md:col-3 lg:col content pr-4 pl-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      autoFocus
                      className="vw-input"
                      autoComplete="off"
                      required={true}
                      name="firstname"
                      value={values?.firstname}
                      onChange={handleInputChange}
                      disabled={true}
                    />
                    <label>Name appeared on Certificate *</label>
                    {formSubmitted && !values?.firstname && (
                      <small className="p-error">Please enter name</small>
                    )}
                  </span>
                </div>
              </div>
              {/* <div className="col-12 md:col-3 lg:col content pl-4 pr-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      className="vw-input"
                      autoComplete="off"
                      required={true}
                      name="middlename"
                      value={values?.middlename}
                      onChange={handleInputChange}
                    />
                    <label>Father's Name *</label>
                  </span>
                  {formSubmitted && !values?.middlename && (
                    <small className="p-error">
                      Please enter father's name
                    </small>
                  )}
                </div>
              </div> */}
              {/* <div className="col-12 md:col-3 lg:col content pl-4 pr-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      className="vw-input"
                      autoComplete="off"
                      required={false}
                      name="lastname"
                      value={values?.lastname}
                      onChange={handleInputChange}
                    />
                    <label>Surname</label>
                  </span>
                </div>
              </div> */}
            </div>
            <div className="grid">
              <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      className="vw-input"
                      autoComplete="off"
                      required={true}
                      name="mobile_number"
                      value={values?.mobile_number}
                      onChange={handleInputChange}
                      maxLength={10}
                      disabled={true}
                    />
                    <label>Mobile No. *</label>
                  </span>
                  {formSubmitted && !values?.mobile_number && (
                    <small className="p-error">
                      Please enter mobile number
                    </small>
                  )}
                  {formSubmitted && isNaN(values?.mobile_number) && (
                    <small className="p-error">
                      Mobile Number must be in numeric format
                    </small>
                  )}
                </div>
              </div>
              <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                <div>
                  <span className="p-float-label">
                    <InputText
                      className="vw-input"
                      autoComplete="off"
                      required={true}
                      name="email"
                      value={values?.email}
                      onChange={handleInputChange}
                      disabled={true}
                    />
                    <label>Email Address *</label>
                  </span>
                  {formSubmitted && !values?.email && (
                    <small className="p-error">
                      Please enter email address.
                    </small>
                  )}
                  {formSubmitted &&
                    values?.email &&
                    !validateEmail(values?.email) && (
                      <small className="p-error">Invalid Email Address</small>
                    )}
                </div>
              </div>
              <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
                <div>
                  <span className="p-float-label">
                    <Password
                      toggleMask
                      feedback={false}
                      type="password"
                      className="vw-input-password"
                      autoComplete="off"
                      required={false}
                      name="password"
                      value={!!values?.password ? values?.password : ""}
                      onChange={handleInputChange}
                    />
                    <label>Update Password</label>
                  </span>
                  {/* {formSubmitted && !values?.password && (
                                            <small className="p-error">
                                                Please enter password
                                            </small>
                                        )} */}
                </div>
              </div>
            </div>
            <div className="grid">
              <div className="col-12 md:col-4 lg:col-4 content pr-4 pl-4">
                <div>
                  <span className="p-float-label vw-calendar">
                    <Calendar
                      style={{ width: "100%" }}
                      inputId="date_of_birth"
                      value={getDateObject(values?.date_of_birth)}
                      name="date_of_birth"
                      onChange={handleInputChange}
                      dateFormat="dd-mm-yy"
                      monthNavigator
                      yearNavigator
                      yearRange={`1924:${moment().format("YYYY")}`}
                      showIcon
                      disabled={true}
                    />

                    <label htmlFor="birth_date">Date Of Birth *</label>
                  </span>
                  {formSubmitted && !values?.date_of_birth && (
                    <small className="p-error">
                      Please enter Date Of Birth
                    </small>
                  )}
                </div>
              </div>
              <div className="col-12 md:col-4 lg:col-4 content pr-4 pl-4">
                <div className="card flex">
                  <div className="flex flex-wrap gap-3 mt-3">
                    {values?.certificate_status === "Old Certificate" && (
                      <div className="flex align-items-center">
                        <RadioButton
                          inputId="oldCertificate"
                          disabled
                          name="certificate_status"
                          value="Old Certificate"
                          onChange={handleInputChange}
                          checked={
                            values?.certificate_status === "Old Certificate"
                          }
                        />
                        <label htmlFor="oldCertificate" className="ml-2">
                          Already Registered with CCRYN?
                        </label>
                      </div>
                    )}
                    {values?.certificate_status === "New Certificate" && (
                      <div className="flex align-items-center">
                        <RadioButton
                          inputId="newCertificate"
                          disabled
                          name="certificate_status"
                          value="New Certificate"
                          onChange={handleInputChange}
                          checked={
                            values?.certificate_status === "New Certificate"
                          }
                        />
                        <label htmlFor="newCertificate" className="ml-2">
                          New Registration
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="col-12 md:col-4 lg:col-4 content pr-4 pl-4">
                <div className="card flex">
                  <div className="flex flex-wrap gap-3 mt-3">
                    {values?.category === "Naturopathy Doctors" && (
                      <div className="flex align-items-center">
                        <RadioButton
                          inputId="category_doctors"
                          name="category"
                          value="Naturopathy Doctors"
                          onChange={handleInputChange}
                          checked={values?.category === "Naturopathy Doctors"}
                        />
                        <label htmlFor="category_doctors" className="ml-2">
                          Naturopathy Doctors
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="grid">
                            <div className="col-12 md:col-6 lg:col content pr-4 pl-4">
                                <div className="flex">
                                    <FileUpload
                                        name="profileImage"
                                        className="fileUpload"
                                        mode="basic"
                                        customUpload
                                        auto
                                        uploadHandler={(event) =>
                                            myUploader(event, "profileImage")
                                        }
                                        // maxFileSize={2000000}
                                        // maxFileSize={2000}
                                        // onUpload={(event) =>
                                        //     myUploader(event, "profileImage")
                                        // }
                                        chooseLabel="Choose Profile Image"
                                        accept=".png, .PNG, .jpeg, .jpg, .JPEG, .JPG"
                                    />
                                    <label className="ml-4 mt-2">
                                        <strong>
                                            {values?.profileImageName}
                                        </strong>
                                    </label>
                                </div>
                                <div>
                                    {values?.profileImagePath && (
                                        <img
                                            className="ml-4"
                                            src={`${process.env.REACT_APP_API_BASE_URL}/fetchImages/${values?.profileImagePath}`}
                                            alt="Profile"
                                            style={{
                                                width: "auto",
                                                height: "100px",
                                            }}
                                        />
                                    )}
                                </div>
                            </div>

                            <div className="col-12 md:col-6 lg:col content pr-4 pl-4">
                                <div className="flex">
                                    <FileUpload
                                        name="signatureImage"
                                        className="fileUpload"
                                        mode="basic"
                                        customUpload
                                        auto
                                        uploadHandler={(event) =>
                                            myUploader(event, "signatureImage")
                                        }
                                        // onUpload={(event) =>
                                        //     myUploader(event, "profileImage")
                                        // }
                                        // maxFileSize={2000000}

                                        chooseLabel="Choose Signature Image"
                                        accept=".png, .PNG, .jpeg, .jpg, .JPEG, .JPG"
                                    />
                                    <label className="ml-4 mt-2">
                                        <strong>
                                            {values?.signatureImageName}
                                        </strong>
                                    </label>
                                </div>
                                <div>
                                    {values?.signatureImagePath && (
                                        <img
                                            className="ml-4"
                                            src={`${process.env.REACT_APP_API_BASE_URL}/fetchImages/${values?.signatureImagePath}`}
                                            alt="Signature"
                                            style={{
                                                width: "auto",
                                                height: "100px",
                                            }}
                                        />
                                    )}
                                </div>
                            </div>
                        </div> */}
          </Card>
        </form>
      </div>
    </div>
  );
  // return (
  //     <div className="center-align">
  //         {isLoading && <Loading />}
  //         <Toast ref={toast} className="ToastMessage" />
  //         <div className="RegisterForm">
  //             <form method="post" onSubmit={handleSubmit}>
  //                 <Card
  //                     title="My Profile"
  //                     // subTitle="You can only register if you are citizen of India"
  //                     footer={cardFooter}
  //                 >
  //                     <div className="grid">
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         autoFocus
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="firstname"
  //                                         value={values?.firstname}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>First Name *</label>
  //                                     {formSubmitted &&
  //                                         !values?.firstname && (
  //                                             <small className="p-error">
  //                                                 Please enter first name
  //                                             </small>
  //                                         )}
  //                                 </span>
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="middlename"
  //                                         value={values?.middlename}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Middle Name *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.middlename && (
  //                                     <small className="p-error">
  //                                         Please enter middlename
  //                                     </small>
  //                                 )}
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="lastname"
  //                                         value={values?.lastname}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Last Name *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.lastname && (
  //                                     <small className="p-error">
  //                                         Please enter lastname
  //                                     </small>
  //                                 )}
  //                             </div>
  //                         </div>
  //                     </div>
  //                     <div className="grid">
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="mobile_number"
  //                                         value={values?.mobile_number}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Mobile No. *</label>
  //                                 </span>
  //                                 {formSubmitted &&
  //                                     !values?.mobile_number && (
  //                                         <small className="p-error">
  //                                             Please enter mobile number
  //                                         </small>
  //                                     )}
  //                                 {formSubmitted &&
  //                                     isNaN(values?.mobile_number) && (
  //                                         <small className="p-error">
  //                                             Mobile Number must be in numeric
  //                                             format
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="email"
  //                                         value={values?.email}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Email Address *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.email && (
  //                                     <small className="p-error">
  //                                         Please enter email address.
  //                                     </small>
  //                                 )}
  //                                 {formSubmitted &&
  //                                     values?.email &&
  //                                     !validateEmail(values?.email) && (
  //                                         <small className="p-error">
  //                                             Invalid Email Address
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <Password
  //                                         toggleMask
  //                                         feedback={false}
  //                                         type="password"
  //                                         className="vw-input-password"
  //                                         autoComplete="off"
  //                                         required={false}
  //                                         name="password"
  //                                         value={
  //                                             !!values?.password
  //                                                 ? values?.password
  //                                                 : ""
  //                                         }
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Update Password</label>
  //                                 </span>
  //                                 {/* {formSubmitted && !values?.password && (
  //                                         <small className="p-error">
  //                                             Please enter password
  //                                         </small>
  //                                     )} */}
  //                             </div>
  //                         </div>
  //                     </div>
  //                     <div className="grid">
  //                         {/* <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <Dropdown
  //                                         className="vw-dropdown"
  //                                         value={values?.category_name}
  //                                         options={categories}
  //                                         required={true}
  //                                         name="category_name"
  //                                         onChange={handleInputChange}
  //                                         optionLabel="name"
  //                                         optionValue="name"
  //                                         // placeholder="Select a Country"
  //                                     />
  //                                     <label>Category *</label>
  //                                 </span>
  //                                 {formSubmitted &&
  //                                     !values?.category_name && (
  //                                         <small className="p-error">
  //                                             Please select category
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div> */}
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label vw-calendar">
  //                                     <Calendar
  //                                         style={{ width: "100%" }}
  //                                         inputId="date_of_birth"
  //                                         value={
  //                                             new Date(values?.date_of_birth)
  //                                         }
  //                                         name="date_of_birth"
  //                                         onChange={handleInputChange}
  //                                         dateFormat="dd-mm-yy"
  //                                         showIcon
  //                                         monthNavigator
  //                                         yearNavigator
  //                                         yearRange={`1970:${moment().format(
  //                                             "YYYY"
  //                                         )}`}
  //                                     />

  //                                     <label htmlFor="birth_date">
  //                                         Date Of Birth *
  //                                     </label>
  //                                 </span>
  //                                 {formSubmitted &&
  //                                     !values?.date_of_birth && (
  //                                         <small className="p-error">
  //                                             Please enter Date Of Birth
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div>
  //                         {/* <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                                 <FileUpload
  //                                     ref={dateOfBirthRef}
  //                                     mode="basic"
  //                                     name="dateOfBirthImage"
  //                                     // url="/api/upload"
  //                                     removeIcon
  //                                     // className="fileUpload"
  //                                     customUpload
  //                                     onSelect={(event) =>
  //                                         myUploader(event, "dateOfBirthImage")
  //                                     }
  //                                     // auto
  //                                     chooseLabel="Choose Date of Birth"
  //                                     accept="pdf/*"
  //                                     // uploadHandler={(event) =>
  //                                     //     myUploader(event, "dateOfBirthImage")
  //                                     // }
  //                                 />
  //                                 {formSubmitted &&
  //                                     !values?.dateOfBirthImage?.name && (
  //                                         <small className="p-error">
  //                                             Please select Date of Birth Proof
  //                                         </small>
  //                                     )}
  //                                  <Button
  //                                     type="button"
  //                                     label="Clear"
  //                                     onClick={clearImage}
  //                                     className="p-button-raised p-button-lg mr-4"
  //                                 />
  //                             </div> */}
  //                     </div>
  //                     <div className="grid">
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="street1"
  //                                         value={values?.street1}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Street 1 *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.street1 && (
  //                                     <small className="p-error">
  //                                         Please enter Street 1
  //                                     </small>
  //                                 )}
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={false}
  //                                         name="street2"
  //                                         value={values?.street2}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Street 2</label>
  //                                 </span>
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="city"
  //                                         value={values?.city}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>City *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.city && (
  //                                     <small className="p-error">
  //                                         Please enter city
  //                                     </small>
  //                                 )}
  //                             </div>
  //                         </div>
  //                         <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="state"
  //                                         value={values?.state}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>State *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.state && (
  //                                     <small className="p-error">
  //                                         Please enter state
  //                                     </small>
  //                                 )}
  //                             </div>
  //                         </div>

  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="postcode"
  //                                         value={values?.postcode}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Post Code *</label>
  //                                 </span>
  //                                 {formSubmitted && !values?.postcode && (
  //                                     <small className="p-error">
  //                                         Please enter postcode code
  //                                     </small>
  //                                 )}
  //                                 {formSubmitted &&
  //                                     isNaN(values?.postcode) && (
  //                                         <small className="p-error">
  //                                             Post code must be in numeric
  //                                             format
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div>
  //                         {/* <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                                 <FileUpload
  //                                     mode="basic"
  //                                     name="addressProofImage"
  //                                     // url="/api/upload"
  //                                     // className="fileUpload"
  //                                     customUpload
  //                                     // onUpload={(event) =>
  //                                     //     myUploader(event, "profileImage")
  //                                     // }
  //                                     // auto
  //                                     chooseLabel="Choose Address Proof"
  //                                     accept="pdf/*"
  //                                     uploadHandler={(event) =>
  //                                         myUploader(event, "addressProofImage")
  //                                     }
  //                                 />
  //                                 {formSubmitted &&
  //                                     !values?.addressProofImage?.name && (
  //                                         <small className="p-error">
  //                                             Please select Address Proof
  //                                         </small>
  //                                     )}
  //                             </div> */}
  //                         <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                             <div>
  //                                 <span className="p-float-label">
  //                                     <InputText
  //                                         className="vw-input"
  //                                         autoComplete="off"
  //                                         required={true}
  //                                         name="aadhar_number"
  //                                         value={values?.aadhar_number}
  //                                         onChange={handleInputChange}
  //                                     />
  //                                     <label>Aadhar Number *</label>
  //                                 </span>
  //                                 {formSubmitted &&
  //                                     !values?.aadhar_number && (
  //                                         <small className="p-error">
  //                                             Please enter aadhar number
  //                                         </small>
  //                                     )}
  //                                 {formSubmitted &&
  //                                     isNaN(values?.aadhar_number) && (
  //                                         <small className="p-error">
  //                                             Aadhar Number must be in numeric
  //                                             format
  //                                         </small>
  //                                     )}

  //                                 {formSubmitted &&
  //                                     !isNaN(values?.aadhar_number) &&
  //                                     values?.aadhar_number.length !== 12 && (
  //                                         <small className="p-error">
  //                                             Invalid Aadhar Number
  //                                         </small>
  //                                     )}
  //                             </div>
  //                         </div>
  //                         {/* <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
  //                                 <FileUpload
  //                                     mode="basic"
  //                                     name="aadharProof"
  //                                     // url="/api/upload"
  //                                     // className="fileUpload"
  //                                     customUpload
  //                                     // onUpload={(event) =>
  //                                     //     myUploader(event, "profileImage")
  //                                     // }
  //                                     // auto
  //                                     chooseLabel="Choose Aadhar Proof"
  //                                     accept="pdf/*"
  //                                     uploadHandler={(event) =>
  //                                         myUploader(event, "aadharProof")
  //                                     }
  //                                 />
  //                                 {formSubmitted &&
  //                                     !values?.dateOfBirthImage?.name && (
  //                                         <small className="p-error">
  //                                             Please select Aadhar Proof
  //                                         </small>
  //                                     )}
  //                             </div> */}
  //                     </div>
  //                     {/* <div className="grid">
  //                             <div className="col-12 md:col-6 lg:col content pr-4 pl-4">
  //                                 <div>
  //                                     <span className="p-float-label">
  //                                         <InputText
  //                                             className="vw-input"
  //                                             autoComplete="off"
  //                                             required={true}
  //                                             name="phoneNumber"
  //                                             value={values?.phoneNumber}
  //                                             onChange={handleInputChange}
  //                                         />
  //                                         <label>Phone No. *</label>
  //                                     </span>
  //                                     {formSubmitted && !values?.phoneNumber && (
  //                                         <small className="p-error">
  //                                             Please enter phone number
  //                                         </small>
  //                                     )}
  //                                 </div>
  //                             </div>
  //                             <div className="col-12 md:col-6 lg:col content pl-4 pr-4">
  //                                 <div>
  //                                     <span className="p-float-label">
  //                                         <InputText
  //                                             className="vw-input"
  //                                             autoComplete="off"
  //                                             required={true}
  //                                             name="address"
  //                                             value={values?.address}
  //                                             onChange={handleInputChange}
  //                                         />
  //                                         <label>Address *</label>
  //                                     </span>
  //                                     {formSubmitted && !values?.address && (
  //                                         <small className="p-error">
  //                                             Please enter address
  //                                         </small>
  //                                     )}
  //                                 </div>
  //                             </div>
  //                         </div>

  //                         <div className="grid">
  //                             <div className="col-12 md:col-6 lg:col content pr-4 pl-4">
  //                                 <div>
  //                                     <span className="p-float-label">
  //                                         <InputText
  //                                             className="vw-input"
  //                                             autoComplete="off"
  //                                             required={true}
  //                                             name="city"
  //                                             value={values?.city}
  //                                             onChange={handleInputChange}
  //                                         />
  //                                         <label>City *</label>
  //                                     </span>
  //                                     {formSubmitted && !values?.city && (
  //                                         <small className="p-error">
  //                                             Please enter city
  //                                         </small>
  //                                     )}
  //                                 </div>
  //                             </div>
  //                             <div className="col-12 md:col-6 lg:col content pl-4 pr-4">
  //                                 <div>
  //                                     <span className="p-float-label">
  //                                         <InputText
  //                                             className="vw-input"
  //                                             autoComplete="off"
  //                                             required={true}
  //                                             name="state"
  //                                             value={values?.state}
  //                                             onChange={handleInputChange}
  //                                         />
  //                                         <label>State *</label>
  //                                     </span>
  //                                     {formSubmitted && !values?.state && (
  //                                         <small className="p-error">
  //                                             Please enter state
  //                                         </small>
  //                                     )}
  //                                 </div>
  //                             </div>
  //                         </div> */}

  //                     <div className="grid">
  //                         <div className="col-12 md:col-6 lg:col content pr-4 pl-4">
  //                             <div className="flex">
  //                                 <FileUpload
  //                                     name="profileImage"
  //                                     className="fileUpload"
  //                                     mode="basic"
  //                                     customUpload
  //                                     auto
  //                                     uploadHandler={(event) =>
  //                                         myUploader(event, "profileImage")
  //                                     }
  //                                     // onUpload={(event) =>
  //                                     //     myUploader(event, "profileImage")
  //                                     // }
  //                                     chooseLabel="Choose Profile Image"
  //                                     accept="image/*"
  //                                 />
  //                                 <label className="ml-4 mt-2">
  //                                     <strong>
  //                                         {values?.profileImageName}
  //                                     </strong>
  //                                 </label>
  //                             </div>
  //                             <div>
  //                                 {values?.profileImagePath && (
  //                                     <img
  //                                         className="ml-4"
  //                                         src={`${process.env.REACT_APP_API_BASE_URL}/fetchImages/${values?.profileImagePath}`}
  //                                         alt="Profile"
  //                                         style={{
  //                                             width: "auto",
  //                                             height: "100px",
  //                                         }}
  //                                     />
  //                                 )}
  //                             </div>
  //                         </div>
  //                         {/* <div className="col-12 md:col-6 lg:col content pl-4 pr-4">
  //                                 <div>
  //                                     <span className="p-float-label">
  //                                         <Dropdown
  //                                             className="vw-dropdown"
  //                                             value={values?.country}
  //                                             options={countries}
  //                                             required={true}
  //                                             name="country"
  //                                             onChange={handleInputChange}
  //                                             optionLabel="name"
  //                                             optionValue="name"
  //                                             // placeholder="Select a Country"
  //                                         />
  //                                         <label>Country *</label>
  //                                     </span>
  //                                     {formSubmitted && !values?.country && (
  //                                         <small className="p-error">
  //                                             Please select country
  //                                         </small>
  //                                     )}
  //                                 </div>
  //                             </div> */}
  //                     </div>
  //                 </Card>
  //             </form>
  //         </div>
  //     </div>
  // );
};

export default MyProfile;
