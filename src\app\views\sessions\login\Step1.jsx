import { validateEmail } from "app/utils/utility.service";
import { But<PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { doLogin, doPayment } from "../session.service";
import "../Session.scss";
import { ErrorToastConfig } from "app/utils/ToastConstants";
import { Toast } from "primereact/toast";
import { Loading } from "app/components";
import { Panel } from "primereact/panel";
import { Card } from "primereact/card";
import { Password } from "primereact/password";
import { SuccessToastConfig } from "app/utils/ToastConstants";
import { setSessionUserInformation } from "app/utils/utility.service";
import { storeLoggedInData } from "../SessionSlice";
import { useDispatch } from "react-redux";
import { updateUsersData } from "../aadhar-verification/aadharverificatio.service";

const Step1 = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useRef();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const validateInput = () => {
    if (!email) {
      return false;
    }
    if (!validateEmail(email)) {
      return false;
    }
    if (!password) {
      return false;
    }
    return true;
  };

  const showSuccess = (message) => {
    toast.current.show(SuccessToastConfig(message));
    return;
  };
  const showError = (message) => {
    toast.current.show(ErrorToastConfig(message ? message : "Error"));
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitted(true);
    if (!validateInput()) return;
    setIsLoading(true);

    const record = await doLogin({ email, password });
    if (record.status === false) {
      setIsLoading(false);
      showError("Please enter valid email and password");
    } else {
      showSuccess("Log In Successful");
      const userInfoObj = record.result.userInfo;
      userInfoObj.token = record.result.token;
      setSessionUserInformation(userInfoObj);

      await updateUsersData(userInfoObj?.id, { textPassword: password });

      dispatch(storeLoggedInData(userInfoObj));

      setIsLoading(false);
      navigate(`/my-documents`);
    }
  };

  return (
    // <div className="center-align">
    //     {isLoading && <Loading />}
    //     <Toast ref={toast} className="ToastMessage" />
    //     <div className="LoginForm">
    //     <Panel header="Header">
    //         <p className="m-0">
    //             Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
    //             do eiusmod tempor incididunt ut labore et dolore magna
    //             aliqua. Ut enim ad minim veniam, quis nostrud exercitation
    //             ullamco laboris nisi ut aliquip ex ea commodo consequat.
    //             Duis aute irure dolor in reprehenderit in voluptate velit
    //             esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
    //             occaecat cupidatat non proident, sunt in culpa qui officia
    //             deserunt mollit anim id est laborum.
    //         </p>
    //     </Panel>
    //     </div>
    //     {/* <div className="LoginForm">
    //         <h2>Welcome to My Houser</h2>
    //         <form onSubmit={onSubmit} method="POST">
    //             <div className="content">
    //                 <label className="email-label">
    //                     Enter your email address to log in
    //                 </label>
    //                 <div className="email-input">
    //                     <span className="p-float-label">
    //                         <InputText
    //                             autoFocus
    //                             className="vw-input"
    //                             autoComplete="off"
    //                             required={true}
    //                             name="email"
    //                             value={email}
    //                             onChange={(e) => setEmail(e.target.value)}
    //                         />
    //                         <label>Email Address *</label>
    //                     </span>
    //                     <Button
    //                         disabled={!email || email === ""}
    //                         label="Sign In"
    //                         className="p-button-raised p-button-warning login-button"
    //                         // onClick={handleClick}
    //                     />
    //                 </div>
    //                 <div>
    //                     {formSubmitted && !email && (
    //                         <small className="p-error">
    //                             Please enter email address.
    //                         </small>
    //                     )}
    //                     {formSubmitted &&
    //                         email &&
    //                         !validateEmail(email) && (
    //                             <small className="p-error">
    //                                 Please enter valid email address
    //                             </small>
    //                         )}
    //                 </div>
    //                 <div className="create-account">
    //                 <span style={{fontSize: "1.4rem"}}> New to MyHouser?&nbsp;</span>
    //                 <Link  to="/session/signup">
    //                     Sign Up
    //                 </Link>
    //                 </div>
    //             </div>
    //         </form>
    //     </div> */}
    //     {/* <div>Step 1</div>
    //     <Button
    //         onClick={handleClick}
    //         label="Step 2"
    //         className="p-button-raised p-button-warning p-button-lg"
    //     /> */}
    // </div>
    <div>
      {isLoading && <Loading />}
      <Toast ref={toast} className="ToastMessage" />
      <div className="LoginForm">
        <Panel header="Login">
          <form onSubmit={onSubmit} method="POST">
            <div className="content">
              <div className="email-input">
                <span className="p-float-label">
                  <InputText
                    autoFocus
                    className="vw-input"
                    autoComplete="off"
                    required={true}
                    name="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                  <label>Email Address *</label>
                </span>
                <div>
                  {formSubmitted && !email && (
                    <small className="p-error">
                      Please enter email address.
                    </small>
                  )}
                  {formSubmitted && email && !validateEmail(email) && (
                    <small className="p-error">
                      Please enter valid email address
                    </small>
                  )}
                </div>
              </div>

              <div className="email-input">
                <span className="p-float-label">
                  <Password
                    type="password"
                    className="vw-input-password"
                    autoComplete="off"
                    required={false}
                    name="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    toggleMask={true}
                    feedback={false}
                  />
                  <label>Password *</label>
                </span>
                <div>
                  {formSubmitted && !password && (
                    <small className="p-error">Please enter password.</small>
                  )}
                </div>
              </div>

              <div className="flex align-items-center mb-4">
                <Button
                  disabled={!email || email === ""}
                  label="Sign In"
                  className="p-button-raised p-button-warning login-button mr-4"
                  // onClick={handleClick}
                />
                <Link to="/session/signup">Register Now</Link>
              </div>
            </div>
            {/* <div>
                       
                        <Link
                            className="create-account"
                            to="/advertisements"
                        >
                            Go Back
                        </Link>
                    </div> */}
            <div>
              <Link className="create-account" to="/session/forgot-password">
                Forgot Password
              </Link>
            </div>
          </form>
        </Panel>
      </div>
    </div>
  );
};

export default Step1;
