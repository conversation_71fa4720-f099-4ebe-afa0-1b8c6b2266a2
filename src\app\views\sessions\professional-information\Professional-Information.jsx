import { useEffect, useState } from "react";
import { useFormik, us } from "formik";
import { Dropdown } from "primereact/dropdown";
import * as Yup from "yup";
import { SuccessToastConfig, ErrorToastConfig } from "app/utils/ToastConstants";
import { Toast } from "primereact/toast";

import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Card } from "primereact/card";
import {
  getSpokenLanguages,
  getAllStates,
  getAllDistricts,
  getAllCategories,
  getAllCouncils,
  getAllCourses,
  getAllColleges,
  getAllUniversities,
} from "./professional-info.service";
import { ProgressSpinner } from "primereact/progressspinner";
import {
  getUserData,
  updateUsersData,
} from "../aadhar-verification/aadharverificatio.service";
import dayjs from "dayjs";
import moment from "moment";
import { getDateObject } from "app/utils/utility.service";
import { QUALIFICATION_LIST } from "app/utils/Constants";
import { SYSTEM_OF_MEDICINE_LIST } from "app/utils/Constants";

const Professionalnformation = ({ userId }) => {
  const [loading, setLoading] = useState(true); // Loader state

  const [languages, setLanguages] = useState([]);
  const [states, setStates] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [councils, setCouncils] = useState([]);
  const [courses, setCourses] = useState([]);
  const [colleges, setColleges] = useState([]);
  const [universities, setUniversities] = useState([]);
  const [toast, setToast] = useState(null);

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!userId) return; // Skip fetching if no userId

      setLoading(true);
      try {
        const userData = await getUserData(userId);
        if (userData) {
          formik.setValues({
            healthProfessionalType: userData.healthProfessionalType || "",
            salutation: userData.salutation || "",
            languagesSpoken: userData.languagesSpoken || "",
            state_id: userData.state_id || "",
            district_id: userData.district_id || "",
            pincode: userData.pincode || "",
            category: userData.category || "",
            registeredWithCouncil: userData.registeredWithCouncil || "",
            registrationNumber: userData.registrationNumber || "",
            registrationDate: userData.registrationDate || "",
            isPermanentOrRenewable: userData.isPermanentOrRenewable || "",
            renewableDueDate: userData.renewableDueDate || "",
            nameOfDegreeOrDiplomaObtained:
              userData.nameOfDegreeOrDiplomaObtained || "",
            college: userData.college || "",
            university: userData.university || "",
            yearOfAwardingDegreeDiploma:
              userData.yearOfAwardingDegreeDiploma || "",
            currentlyWorking: userData.currentlyWorking || "",
            chooseWorkStatus: userData.chooseWorkStatus || "",
            reasonForNotWorking: userData.reasonForNotWorking || "",
            state_registration_date: userData.state_registration_date || "",
            state_registration_number: userData.state_registration_number || "",
            awarding_body: userData.awarding_body || "",
            system_of_medicine: userData.system_of_medicine || "",
            name_state_board: userData.name_state_board || "",
            qualification: userData.qualification || "",
          });
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [userId]); // Fetch when userId changes

  const formik = useFormik({
    initialValues: {
      healthProfessionalType: "",
      salutation: "",
      languagesSpoken: "",
      state_id: "",
      district_id: "",
      pincode: "",
      category: "",
      registeredWithCouncil: "",
      registrationNumber: "",
      isPermanentOrRenewable: "",
      registrationDate: "",
      renewableDueDate: "",
      nameOfDegreeOrDiplomaObtained: "",
      college: "",
      university: "",
      yearOfAwardingDegreeDiploma: "",
      currentlyWorking: "",
      chooseWorkStatus: "",
      reasonForNotWorking: "",
      state_registration_date: "",
      state_registration_number: "",
      awarding_body: "",
      system_of_medicine: "",
      name_state_board: "",
      qualification: "",
    },
    validationSchema: Yup.object({
      healthProfessionalType: Yup.string().required("Required"),
      salutation: Yup.string().required("Required"),
      languagesSpoken: Yup.string().required("Select at least one language"),
      state_id: Yup.string().required("Required"),
      district_id: Yup.string().required("Required"),
      pincode: Yup.string()
        .matches(/^\d{6}$/, "Invalid Pincode (6 digits required)")
        .required("Required"),
      category: Yup.string().required("Required"),
      registeredWithCouncil: Yup.string().required("Required"),
      registrationNumber: Yup.string().required("Required"),
      registrationDate: Yup.date().required("Required"),
      isPermanentOrRenewable: Yup.string().required("Required"),
      renewableDueDate: Yup.date().when("isPermanentOrRenewable", {
        is: "Renewable",
        then: (schema) => schema.required("Required"),
      }),
      nameOfDegreeOrDiplomaObtained: Yup.string().required("Required"),
      college: Yup.string().required("Required"),
      university: Yup.string().required("Required"),
      yearOfAwardingDegreeDiploma: Yup.number()
        .typeError("Enter a valid year")
        .min(1900, "Year must be after 1900")
        .max(new Date().getFullYear(), "Year cannot be in the future")
        .required("Required"),
      currentlyWorking: Yup.string().required("Required"),
      chooseWorkStatus: Yup.string().required("Required"),
      reasonForNotWorking: Yup.string().when("currentlyWorking", {
        is: "0",
        then: (schema) => schema.required("Required when not working"),
      }),
    }),
    onSubmit: async (values) => {
      try {
        const response = await updateUsersData(userId, values);

        if (response) {
          toast?.show({
            severity: "success",
            summary: "User updated successfully",
            detail: "User data has been updated successfully!",
          });
        } else {
          throw new Error("No response received");
        }
      } catch (error) {
        console.error("Error updating user data:", error);

        toast?.show({
          severity: "error",
          summary: "Update Failed",
          detail: error.message || "An error occurred. Please try again later!",
        });
      }
    },
  });

  useEffect(() => {
    async function fetchData() {
      setLoading(true); // Start loading

      try {
        const [
          languagesRes,
          statesRes,
          categoriesRes,
          councilsRes,
          coursesRes,
        ] = await Promise.all([
          getSpokenLanguages(),
          getAllStates(),
          getAllCategories(),
          getAllCouncils(),
          getAllCourses(),
        ]);

        setLanguages(languagesRes);
        setStates(statesRes);
        setCategories(categoriesRes);
        setCouncils(councilsRes);
        setCourses(coursesRes);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false); // Stop loading
      }
    }

    fetchData();
  }, []);

  useEffect(async () => {
    if (formik.values.state_id) {
      setLoading(true);

      getAllDistricts(formik.values.state_id)
        .then(setDistricts)
        .finally(() => setLoading(false));

      getAllColleges(formik.values.state_id)
        .then(setColleges)
        .finally(() => setLoading(false));
    } else {
      setDistricts([]);
      setColleges([]);
    }
  }, [formik.values.state_id]);

  useEffect(() => {
    if (formik.values.college) {
      setLoading(true);
      getAllUniversities(formik.values.college)
        .then(setUniversities)
        .finally(() => setLoading(false));
    } else {
      setUniversities([]);
    }
  }, [formik.values.college]);
  const handleLanguageChange = (e) => {
    const newValue = e.value;
    const currentSelections = formik.values.languagesSpoken
      ? formik.values.languagesSpoken.split(",")
      : [];

    const index = currentSelections.indexOf(String(newValue));

    if (index > -1) {
      currentSelections.splice(index, 1);
    } else {
      currentSelections.push(newValue);
    }

    formik.setFieldValue("languagesSpoken", currentSelections.join(","));
  };

  // 4. Create a helper function to convert from string to array for the value prop
  const getSelectedLanguageNames = () => {
    if (!formik.values.languagesSpoken) return [];
    const selectedIds = formik.values.languagesSpoken.split(",");
    return languages
      ?.filter((lang) => selectedIds.includes(String(lang.id)))
      .map((lang) => lang.name);
  };

  return (
    <Card title="Health Professional Form">
      <Toast ref={(el) => setToast(el)} />

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <ProgressSpinner />
        </div>
      ) : (
        <form onSubmit={formik.handleSubmit}>
          {/* start */}
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <Dropdown
                    className="vw-dropdown"
                    value={formik.values?.qualification}
                    options={QUALIFICATION_LIST}
                    required={true}
                    name="qualification"
                    onChange={formik.handleChange}
                  />
                  <label>Qualification *</label>
                </span>
              </div>
            </div>

            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <InputText
                    className="vw-input"
                    autoComplete="off"
                    required={false}
                    name="name_state_board"
                    value={formik.values?.name_state_board}
                    onChange={formik.handleChange}
                  />
                  <label>Name of the State Board</label>
                </span>
              </div>
            </div>
          </div>
          {/* <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <Dropdown
                    className="vw-dropdown"
                    value={formik.values?.system_of_medicine}
                    options={SYSTEM_OF_MEDICINE_LIST}
                    required={true}
                    name="system_of_medicine"
                    onChange={formik.handleChange}
                  />
                  <label>System Of Medicine *</label>
                </span>
              </div>
            </div>

            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <InputText
                    className="vw-input"
                    autoComplete="off"
                    required={true}
                    name="awarding_body"
                    value={formik.values?.awarding_body}
                    onChange={formik.handleChange}
                  />
                  <label>University *</label>
                </span>
              </div>
            </div>
          </div> */}
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <Dropdown
                    className="vw-dropdown"
                    value={formik.values?.system_of_medicine}
                    options={SYSTEM_OF_MEDICINE_LIST}
                    required={true}
                    name="system_of_medicine"
                    onChange={formik.handleChange}
                  />
                  <label>System Of Medicine *</label>
                </span>
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <InputText
                    className="vw-input"
                    autoComplete="off"
                    required={false}
                    name="state_registration_number"
                    value={formik.values?.state_registration_number}
                    onChange={formik.handleChange}
                  />
                  <label>State Registration Number</label>
                </span>
              </div>
            </div>

            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Calendar
                    style={{ width: "100%" }}
                    inputId="state_registration_date"
                    value={getDateObject(
                      formik.values?.state_registration_date
                    )}
                    name="state_registration_date"
                    onChange={(e) => {
                      const formattedDate = e.value
                        ? dayjs(e.value).format("YYYY-MM-DD")
                        : "";
                      formik.setFieldValue(
                        "state_registration_date",
                        formattedDate
                      );
                    }}
                    dateFormat="dd-mm-yy"
                    monthNavigator
                    yearNavigator
                    yearRange={`1970:${moment().format("YYYY")}`}
                    showIcon
                  />

                  <label htmlFor="state_registration_date">
                    State Registration Date
                  </label>
                </span>
                {/* {formSubmitted &&
                                                  !values?.state_registration_date && (
                                                      <small className="p-error">
                                                          Please enter State Registration Date
                                                      </small>
                                                  )} */}
              </div>
            </div>
          </div>
          {/* end */}
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <Dropdown
                    name="healthProfessionalType"
                    value={formik.values.healthProfessionalType}
                    className="vw-dropdown"
                    onChange={formik.handleChange}
                    options={[
                      { label: "Doctor", value: "doctor" },
                      { label: "Nurse", value: "nurse" },
                    ]}
                  />
                  <label>Health Professional Type</label>
                </span>
                {formik.touched.healthProfessionalType &&
                  formik.errors.healthProfessionalType && (
                    <small className="p-error">
                      {formik.errors.healthProfessionalType}
                    </small>
                  )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label">
                  <Dropdown
                    name="salutation"
                    value={formik.values.salutation}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={[
                      { label: "Mr.", value: "Mr" },
                      { label: "Mrs.", value: "Mrs" },
                    ]}
                  />
                  <label>Salutation</label>
                </span>
                {formik.touched.salutation && formik.errors.salutation && (
                  <small className="p-error">{formik.errors.salutation}</small>
                )}
              </div>
            </div>
          </div>
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="languagesSpoken"
                    value={getSelectedLanguageNames()}
                    onChange={handleLanguageChange}
                    className="vw-dropdown"
                    options={languages?.map((lang) => ({
                      label: lang.name,
                      value: lang.id,
                    }))}
                    multiple
                  />
                  <label>Languages Spoken</label>
                </span>{" "}
                {formik.touched.languagesSpoken &&
                  formik.errors.languagesSpoken && (
                    <small className="p-error">
                      {formik.errors.languagesSpoken}
                    </small>
                  )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4 mt-4">
              <div>
                <span className="p-float-label vw-calendar">
                  {getSelectedLanguageNames()?.map((name) => (
                    <span
                      key={name}
                      style={{
                        backgroundColor: "#007bff",
                        color: "white",
                        padding: "5px 10px",
                        borderRadius: "5px",
                        fontSize: "14px",
                        height: "25px",
                        marginRight: "5px",
                      }}
                    >
                      {name}
                    </span>
                  ))}
                </span>
              </div>
            </div>
          </div>
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="state_id"
                    value={formik.values.state_id}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={states?.map((state) => ({
                      label: state.name,
                      value: state.id,
                    }))}
                    disabled
                  />
                  <label>State</label>
                </span>
                {formik.touched.state_id && formik.errors.state_id && (
                  <small className="p-error">{formik.errors.state_id}</small>
                )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="district_id"
                    value={formik.values.district_id}
                    className="vw-dropdown"
                    onChange={formik.handleChange}
                    options={districts?.map((district) => ({
                      label: district.districtName,
                      value: district.id,
                    }))}
                    disabled
                  />
                  <label>District</label>
                </span>
                {formik.touched.district_id && formik.errors.district_id && (
                  <small className="p-error">{formik.errors.district_id}</small>
                )}
              </div>
            </div>
          </div>

          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <InputText
                    name="pincode"
                    className="vw-input"
                    value={formik.values.pincode}
                    onChange={formik.handleChange}
                  />
                  <label>Pincode</label>
                </span>
                {formik.touched.pincode && formik.errors.pincode && (
                  <small className="p-error">{formik.errors.pincode}</small>
                )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="category"
                    value={formik.values.category}
                    className="vw-dropdown"
                    onChange={formik.handleChange}
                    options={categories?.map((cat) => ({
                      label: cat.name,
                      value: cat.id,
                    }))}
                  />
                  <label>Category</label>
                </span>
                {formik.touched.category && formik.errors.category && (
                  <small className="p-error">{formik.errors.category}</small>
                )}
              </div>
            </div>
          </div>

          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="registeredWithCouncil"
                    value={formik.values.registeredWithCouncil}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={councils?.map((council) => ({
                      label: council.name,
                      value: council.id,
                    }))}
                  />
                  <label>Registered With Council</label>
                </span>
                {formik.touched.registeredWithCouncil &&
                  formik.errors.registeredWithCouncil && (
                    <small className="p-error">
                      {formik.errors.registeredWithCouncil}
                    </small>
                  )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <InputText
                    name="registrationNumber"
                    className="vw-input"
                    value={formik.values.registrationNumber}
                    onChange={formik.handleChange}
                  />
                  <label>Registration Number</label>
                </span>
                {formik.touched.registrationNumber &&
                  formik.errors.registrationNumber && (
                    <small className="p-error">
                      {formik.errors.registrationNumber}
                    </small>
                  )}
              </div>
            </div>
          </div>
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Calendar
                    name="registrationDate"
                    value={getDateObject(formik.values?.registrationDate)}
                    onChange={(e) => {
                      const formattedDate = e.value
                        ? dayjs(e.value).format("YYYY-MM-DD")
                        : "";
                      formik.setFieldValue("registrationDate", formattedDate);
                    }}
                    dateFormat="yy-mm-dd"
                    showIcon
                  />
                  <label>Registration Date</label>
                </span>
                {formik.touched.registrationDate &&
                  formik.errors.registrationDate && (
                    <small className="p-error">
                      {formik.errors.registrationDate}
                    </small>
                  )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="isPermanentOrRenewable"
                    value={formik.values.isPermanentOrRenewable}
                    className="vw-dropdown"
                    onChange={formik.handleChange}
                    options={[
                      { label: "Permanent", value: "Permanent" },
                      { label: "Renewable", value: "Renewable" },
                    ]}
                  />
                  <label>Permanent or Renewable</label>
                </span>
                {formik.touched.isPermanentOrRenewable &&
                  formik.errors.isPermanentOrRenewable && (
                    <small className="p-error">
                      {formik.errors.isPermanentOrRenewable}
                    </small>
                  )}
              </div>
            </div>
          </div>
          {formik.values.isPermanentOrRenewable === "Renewable" && (
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "5px",
                marginBottom: "10px",
              }}
            >
              <Calendar
                name="renewableDueDate"
                value={formik.values.renewableDueDate}
                onChange={formik.handleChange}
              />
              {formik.touched.renewableDueDate &&
                formik.errors.renewableDueDate && (
                  <small className="p-error">
                    {formik.errors.renewableDueDate}
                  </small>
                )}
              <label>Renewable Due Date</label>
            </div>
          )}
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="nameOfDegreeOrDiplomaObtained"
                    value={formik.values.nameOfDegreeOrDiplomaObtained}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={courses?.map((course) => ({
                      label: course.name,
                      value: course.id,
                    }))}
                  />
                  <label>Degree/Diploma</label>
                </span>
                {formik.touched.nameOfDegreeOrDiplomaObtained &&
                  formik.errors.nameOfDegreeOrDiplomaObtained && (
                    <small className="p-error">
                      {formik.errors.nameOfDegreeOrDiplomaObtained}
                    </small>
                  )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="college"
                    value={formik.values.college}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={colleges?.map((college) => ({
                      label: college.name,
                      value: college.id,
                    }))}
                  />
                  <label>College</label>
                </span>
                {formik.touched.college && formik.errors.college && (
                  <small className="p-error">{formik.errors.college}</small>
                )}
              </div>
            </div>
          </div>
          <div className="grid">
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="university"
                    value={formik.values.university}
                    className="vw-dropdown"
                    onChange={formik.handleChange}
                    options={universities?.map((uni) => ({
                      label: uni.name,
                      value: uni.id,
                    }))}
                  />
                  <label>University</label>
                </span>
                {formik.touched.university && formik.errors.university && (
                  <small className="p-error">{formik.errors.university}</small>
                )}
              </div>
            </div>
            <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <InputText
                    name="yearOfAwardingDegreeDiploma"
                    value={formik.values.yearOfAwardingDegreeDiploma}
                    className="vw-input"
                    onChange={formik.handleChange}
                    maxLength={4}
                  />
                  <label>Year of Awarding Degree/Diploma</label>
                </span>
                {formik.touched.yearOfAwardingDegreeDiploma &&
                  formik.errors.yearOfAwardingDegreeDiploma && (
                    <small className="p-error">
                      {formik.errors.yearOfAwardingDegreeDiploma}
                    </small>
                  )}
              </div>
            </div>
          </div>
          <div className="grid">
            {/* Currently Working */}
            <div className="col-12 md:col-6 lg:col-6 content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="currentlyWorking"
                    value={formik.values.currentlyWorking}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={[
                      { label: "Yes", value: 1 },
                      { label: "No", value: 0 },
                    ]}
                  />
                  <label>Currently Working</label>
                </span>
                {formik.touched.currentlyWorking &&
                  formik.errors.currentlyWorking && (
                    <small className="p-error">
                      {formik.errors.currentlyWorking}
                    </small>
                  )}
              </div>
            </div>

            {/* Work Status */}
            <div className="col-12 md:col-6 lg:col-6 content pl-4 pr-4">
              <div>
                <span className="p-float-label vw-calendar">
                  <Dropdown
                    name="chooseWorkStatus"
                    value={formik.values.chooseWorkStatus}
                    onChange={formik.handleChange}
                    className="vw-dropdown"
                    options={[
                      { label: "Both", value: 2 },
                      { label: "Government", value: 1 },
                      { label: "Private", value: 0 },
                    ]}
                  />
                  <label>Work Status</label>
                </span>
                {formik.touched.chooseWorkStatus &&
                  formik.errors.chooseWorkStatus && (
                    <small className="p-error">
                      {formik.errors.chooseWorkStatus}
                    </small>
                  )}
              </div>
            </div>

            {/* Reason for Not Working (Spans Full Width) */}
            {formik.values.currentlyWorking === 0 && (
              <div className="col-12 content pl-4 pr-4">
                <div>
                  <label>Reason for Not Working</label>
                  <InputText
                    name="reasonForNotWorking"
                    value={formik.values.reasonForNotWorking}
                    onChange={formik.handleChange}
                    className="vw-input"
                  />
                  {formik.touched.reasonForNotWorking &&
                    formik.errors.reasonForNotWorking && (
                      <small className="p-error">
                        {formik.errors.reasonForNotWorking}
                      </small>
                    )}
                </div>
              </div>
            )}
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "center", // Centers the button horizontally
              marginTop: "20px", // Adds spacing from the inputs
              gridColumn: "span 2", // Makes it span both columns on larger screens
            }}
          >
            <Button type="submit" label="Submit" className="p-button-lg" />
          </div>
        </form>
      )}
    </Card>
  );
};

export default Professionalnformation;

//   return (
//     <Card title="Health Professional Form">
//       {loading ? (
//         <div className="flex justify-center items-center h-40">
//           <ProgressSpinner />
//         </div>
//       ) : (
//         <form onSubmit={formik.handleSubmit}>
//           <div>
//             <label>Health Professional Type</label>
//             <Dropdown
//               name="healthProfessionalType"
//               value={formik.values.healthProfessionalType}
//               onChange={formik.handleChange}
//               options={[
//                 { label: "Doctor", value: "doctor" },
//                 { label: "Nurse", value: "nurse" },
//               ]}
//               placeholder="Select Type"
//             />
//             {formik.touched.healthProfessionalType &&
//               formik.errors.healthProfessionalType && (
//                 <small className="p-error">
//                   {formik.errors.healthProfessionalType}
//                 </small>
//               )}
//           </div>

//           <div>
//             <label>Salutation</label>
//             <Dropdown
//               name="salutation"
//               value={formik.values.salutation}
//               onChange={formik.handleChange}
//               options={[
//                 { label: "Mr.", value: "Mr" },
//                 { label: "Mrs.", value: "Mrs" },
//               ]}
//               placeholder="Salutation"
//             />
//           </div>

//           <div>
//             <label>Languages Spoken</label>
//             <Dropdown
//               name="languagesSpoken"
//               value={formik.values.languagesSpoken}
//               onChange={formik.handleChange}
//               options={languages.map((lang) => ({
//                 label: lang.name,
//                 value: lang.id,
//               }))}
//               placeholder="Languages Spoken"
//               multiple
//             />
//           </div>

//           <div>
//             <label>State</label>
//             <Dropdown
//               name="state"
//               value={formik.values.state}
//               onChange={formik.handleChange}
//               options={states.map((state) => ({
//                 label: state.name,
//                 value: state.id,
//               }))}
//               placeholder="State"
//             />
//           </div>

//           <div>
//             <label>District</label>
//             <Dropdown
//               name="district"
//               value={formik.values.district}
//               onChange={formik.handleChange}
//               options={districts.map((district) => ({
//                 label: district.districtName,
//                 value: district.id,
//               }))}
//               placeholder="District"
//             />
//           </div>

//           <div>
//             <label>Pincode</label>
//             <InputText
//               name="pincode"
//               value={formik.values.pincode}
//               onChange={formik.handleChange}
//               placeholder="Pincode"
//             />
//           </div>

//           <div>
//             <label>Category</label>
//             <Dropdown
//               name="category"
//               value={formik.values.category}
//               onChange={formik.handleChange}
//               options={categories.map((cat) => ({
//                 label: cat.name,
//                 value: cat.id,
//               }))}
//               placeholder="Category"
//             />
//           </div>

//           <div>
//             <label>Registered With Council</label>
//             <Dropdown
//               name="registeredWithCouncil"
//               value={formik.values.registeredWithCouncil}
//               onChange={formik.handleChange}
//               options={councils.map((council) => ({
//                 label: council.name,
//                 value: council.id,
//               }))}
//               placeholder="Registered With Council"
//             />
//           </div>

//           <div>
//             <label>Registration Number</label>
//             <InputText
//               name="registrationNumber"
//               value={formik.values.registrationNumber}
//               onChange={formik.handleChange}
//               placeholder="Registration Number"
//             />
//           </div>

//           <div>
//             <label>Permanent or Renewable</label>
//             <Dropdown
//               name="isPermanentOrRenewable"
//               value={formik.values.isPermanentOrRenewable}
//               onChange={formik.handleChange}
//               options={[
//                 { label: "Permanent", value: "Permanent" },
//                 { label: "Renewable", value: "Renewable" },
//               ]}
//               placeholder="Permanent or Renewable"
//             />
//           </div>

//           {formik.values.isPermanentOrRenewable === "Renewable" && (
//             <div>
//               <label>Renewable Due Date</label>
//               <Calendar
//                 name="renewableDueDate"
//                 value={formik.values.renewableDueDate}
//                 onChange={formik.handleChange}
//                 placeholder="Renewable Due Date"
//               />
//             </div>
//           )}

//           <div>
//             <label>Degree/Diploma</label>
//             <Dropdown
//               name="nameOfDegreeOrDiplomaObtained"
//               value={formik.values.nameOfDegreeOrDiplomaObtained}
//               onChange={formik.handleChange}
//               options={courses.map((course) => ({
//                 label: course.name,
//                 value: course.id,
//               }))}
//               placeholder="Degree/Diploma"
//             />
//           </div>

//           <div>
//             <label>College</label>
//             <Dropdown
//               name="college"
//               value={formik.values.college}
//               onChange={formik.handleChange}
//               options={colleges.map((college) => ({
//                 label: college.name,
//                 value: college.id,
//               }))}
//               placeholder="College"
//             />
//           </div>

//           <div>
//             <label>University</label>
//             <Dropdown
//               name="university"
//               value={formik.values.university}
//               onChange={formik.handleChange}
//               options={universities.map((uni) => ({
//                 label: uni.name,
//                 value: uni.id,
//               }))}
//               placeholder="University"
//             />
//           </div>

//           <div>
//             <label>Year of Awarding Degree/Diploma</label>
//             <InputText
//               name="yearOfAwardingDegreeDiploma"
//               value={formik.values.yearOfAwardingDegreeDiploma}
//               onChange={formik.handleChange}
//               placeholder="Year of Awarding"
//               maxLength={4}
//             />
//           </div>

//           <div>
//             <label>Currently Working</label>
//             <Dropdown
//               name="currentlyWorking"
//               value={formik.values.currentlyWorking}
//               onChange={formik.handleChange}
//               options={[
//                 { label: "Yes", value: 1 },
//                 { label: "No", value: 0 },
//               ]}
//               placeholder="Currently Working"
//             />
//           </div>

//           {formik.values.currentlyWorking === 0 && (
//             <div>
//               <label>Reason for Not Working</label>
//               <InputText
//                 name="reasonForNotWorking"
//                 value={formik.values.reasonForNotWorking}
//                 onChange={formik.handleChange}
//                 placeholder="Reason for Not Working"
//               />
//             </div>
//           )}

//           <div>
//             <Button type="submit" label="Submit" />
//           </div>
//         </form>
//       )}
//     </Card>
//   );
