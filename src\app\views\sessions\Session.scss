.RegisterForm {
	padding: 2rem;
	width: 70%;
	margin: auto;
	.p-card-title {
		font-weight: 500;
		margin-left: 1rem;
	}
	.content {
		> div {
			margin: 1rem 0;
		}
		> div:last-child {
			margin-bottom: 0;
		}
	}

}

.LoginForm {
	width: 30%;
	margin: auto;

	h2 {
		text-align: center;
		font-size: 2rem;
		margin-bottom: 5rem;
		font-weight: 500;
	}


			.login-button {
                display: flex;
                justify-content: center;
				width: 50% !important;
			}

	.content {
        display: flex;
		justify-content: center;
        flex-direction: column;
		.email-label {
			// display: block;
			// justify-content: center;
			font-size: 1.4rem;
			margin-bottom: 1rem;
			margin-left: 8.5rem;
			font-weight: 400;
		}
		.email-input {
			
			margin-bottom: 2rem;
			span {
				margin-right: 1rem;
			}
		}

		small {
			font-size: 1.2rem;
			margin-top: 0.5rem;
			display: block;
		}

		.create-account {
            display: flex;
            align-items: center;
			// margin-top: 2rem;
			margin-left: 8.5rem;
			// display: block;
		}
	}
}

.ForgotPasswordForm {
	padding: 2rem;
	width: 30%;
	margin: auto;

	h2 {
		display: flex;
		justify-content: center;
		font-size: 2rem;
		margin-bottom: 2rem;
		font-weight: 400;
	}
}
.CertificateTemplate {
    // .certificateBackground {
    //     background-image: url("../../../../public/goldencer.webp");
    //     background-repeat: no-repeat;
    //     height: 842px
    // }
    .p-card  {
        box-shadow: none;
        .p-card-content {
            padding: 7rem;
        }
    }
    .certificateDetails {
        label {
        font-size: 1.3rem;
        color: #040000;
        strong {
            text-transform: uppercase;
        }
        }
    }
}

@media only screen and (min-width: 820px) and (max-width: 1180px) {
	.RegisterForm {
		width: 100%;
	}
	.LoginForm {
		width: 100%;
		.content {
			.email-label,
			.create-account {
				margin-left: 4.5rem;
			}
		}
	}

	.ForgotPasswordForm {
		width: 100%;
	}
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
	.RegisterForm {
		width: 100%;
	}
	.LoginForm {
		width: 100%;
		.content { 
			.email-label,
			.create-account {
				@media (orientation: portrait){
					margin-left: 11.5rem;
				}
				@media (orientation: landscape){
					margin-left: 14.5rem;
				}
			}
			.email-input {
				@media (orientation: landscape){
					display: flex;
					justify-content: left;
					margin-bottom: 2rem;
					margin-left: 14.5rem;
					span {
						width: 30%;
						margin-right: 1rem;
					}
					.login-button {
						width: 10%;
					}
				}
			}
		}
	}

	.ForgotPasswordForm {
		width: 100%;
	}
}
