/* Professional Information Form Styles */
.professional-info-container {
  padding: 1rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.professional-info-card {
  border-radius: 12px;
  border: none;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.professional-info-card .p-card-title {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 1rem;
}

.professional-form {
  padding: 0;
}

.form-section {
  margin-bottom: 2rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
}

.form-section .p-panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
  padding: 1rem 1.5rem;
  font-weight: 600;
  font-size: 1.1rem;
}

.form-section .p-panel-content {
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 0 0 8px 8px;
}

.field {
  margin-bottom: 1.5rem;
}

.field .p-float-label {
  margin-bottom: 0.5rem;
}

.field .p-float-label > label {
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.field .p-inputtext,
.field .p-dropdown,
.field .p-calendar {
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease-in-out;
  font-size: 0.875rem;
}

.field .p-inputtext:focus,
.field .p-dropdown:focus,
.field .p-calendar:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field .p-inputtext:hover,
.field .p-dropdown:hover,
.field .p-calendar:hover {
  border-color: #9ca3af;
}

.field .p-error {
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

/* Language tags styling */
.language-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: 0.5rem;
  margin-bottom: 0.25rem;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Submit button styling */
.submit-button-container {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  padding: 2rem;
  border-radius: 8px;
  margin-top: 2rem;
  text-align: center;
}

.p-button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.p-button-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(0, 0, 0, 0.15);
}

.p-button-primary:active {
  transform: translateY(0);
}

/* Loading spinner styling */
.loading-container {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .professional-info-container {
    padding: 0.5rem;
  }
  
  .professional-info-card {
    margin: 0.5rem;
  }
  
  .form-section .p-panel-content {
    padding: 1rem;
  }
  
  .field {
    margin-bottom: 1rem;
  }
}

/* Panel toggle button styling */
.p-panel .p-panel-header .p-panel-toggler {
  color: white;
  margin-right: 0.5rem;
}

.p-panel .p-panel-header .p-panel-toggler:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* Grid improvements */
.grid {
  margin: 0;
}

.grid > [class*="col-"] {
  padding: 0.5rem;
}

/* Calendar specific styling */
.p-calendar .p-datepicker {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Dropdown specific styling */
.p-dropdown-panel {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.p-dropdown-item:hover {
  background: #f3f4f6;
}

.p-dropdown-item.p-highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Toast styling */
.p-toast .p-toast-message {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
