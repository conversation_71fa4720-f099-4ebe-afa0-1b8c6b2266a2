import React from "react";
import { Redirect } from "react-router-dom";
import sessionRoutes from "./views/sessions/SessionRoutes";
import homePurchaseRoutes from "./views/home-purchase/HomePurchaseRoutes";

const redirectRoute = [
	{
		path: "/",
		exact: true,
		component: () => <Redirect to="/session/signin/step1" />,
	},
];

const errorRoute = [
	{
		component: () => <Redirect to="/session/404" />,
	},
];

const routes = [
	...sessionRoutes,
	...homePurchaseRoutes,
	...redirectRoute,
	...errorRoute,
];

export default routes;
