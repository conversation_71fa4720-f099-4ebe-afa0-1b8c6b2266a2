import moment from "moment";
import LeftTitle from "./LeftTitle";

const CertificateDetails = ({ userValues }) => {
    return (
        <div className="certificateDetails">
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="1" name="System of Medicine" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong> {userValues?.system_of_medicine}</strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="2" name="Central Registration No." />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong>
                            {" "}
                            {userValues?.central_registration_number}
                        </strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="3" name="Name" />
                </div>
                <div className="col-7">
                    <label className="">
                        <span style={{fontWeight: "bold"}}>Dr.</span>
                        <strong>
                            {`${userValues?.firstname}`}
                        </strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="4" name="Father's Name" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong> {userValues?.middlename}</strong>{" "}
                    </label>
                </div>
            </div>

            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="5" name="Date Of Birth" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong>
                            {" "}
                            {`${moment(userValues?.date_of_birth).format(
                                "DD-MMM-YYYY"
                            )}`}
                        </strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="6" name="Qualification/ University" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong> {userValues?.qualification}</strong>/
                    </label>
                    <label style={{ display: "block" }}>
                        <strong>{userValues?.awarding_body}</strong>{" "}
                    </label>
                </div>
            </div>

            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="7" name="State Regn. No. / Date" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong>
                            {" "}
                            {`${
                                userValues?.state_registration_number
                            } ${userValues?.state_registration_date ? ' / '+moment(
                                userValues?.state_registration_date
                            ).format("DD-MMM-YYYY"): "-"}`}
                        </strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
                <div className="col-5">
                    <LeftTitle srno="8" name="Name of State Registration Board" />
                </div>
                <div className="col-7">
                    <label className="">
                        <strong>{userValues?.name_state_board ? userValues?.name_state_board: "-"}</strong>{" "}
                    </label>
                </div>
            </div>
            <div className="grid mb-3 ">
            <div className="col-1" style={{width:"3%", paddingRight: 0, marginRight: 0}}>
                        <label className="">9. </label>
                    </div>
                <div className="col-5" style={{width:"39%", paddingRight: 0, marginRight: 0}}>
                    
                    
                    <label className="">Contact Address</label>
                    <div>
                        <label>
                            <strong>{userValues?.contact_address}</strong>
                        </label>
                    </div>
                </div>
                <div className="col-5" style={{width:"39%", paddingRight: 0, marginRight: 0}}>
                    <label className="">Permanent Address</label>
                    <div>
                    <label>
                            <strong>{userValues?.permanent_address}</strong>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CertificateDetails;
