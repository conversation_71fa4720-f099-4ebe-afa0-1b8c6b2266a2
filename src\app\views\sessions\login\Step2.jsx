import { Loading } from "app/components";
import { SuccessToastConfig } from "app/utils/ToastConstants";
import { ErrorToastConfig } from "app/utils/ToastConstants";
import { setSessionBrokerInformation } from "app/utils/utility.service";
import { setSessionAgentInformation } from "app/utils/utility.service";
import { SETTINGS_CONSTANT_OBJECT } from "app/utils/utility.service";
import { setSessionUserSettings } from "app/utils/utility.service";
import { setSessionUserInformation } from "app/utils/utility.service";
import { convertSettingToNumber } from "app/utils/utility.service";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import { useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { getSingleUser } from "../my-profile/my-profile.service";
import "../Session.scss";
import { doLogin } from "../session.service";
import { storeLoggedInData, storeUserSettings } from "../SessionSlice";
import { Password } from 'primereact/password';


const Step2 = () => {
	const [searchParams] = useSearchParams();
	const email = searchParams.get("email"); // "testCode"
	const [password, setPassword] = useState("");
	const [formSubmitted, setFormSubmitted] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const toast = useRef();
	const navigate = useNavigate();
	const dispatch = useDispatch();
	const validateInput = () => {
		if(!password) {
			return false;
		}
		return true;
	}

	const showSuccess = (message) => {
		toast.current.show(SuccessToastConfig(message));
	};
	const showError = (message) => {
		toast.current.show(ErrorToastConfig(message ? message : "Error"));
	};


	const handleSubmit = async (e) => {
		e.preventDefault();
		setFormSubmitted(true);
		if(!validateInput()) {
			return false;
		}
		setIsLoading(true);
		const payload = { email, password };
		const record = await doLogin(payload);
        if(record.status === false) {
            setIsLoading(false);
            showError("Incorrect password. Please try again or click the Forgot Password link to reset it. ");
        } else {
            showSuccess("Log In Successful");
			const userInfoObj = record.result.userInfo;
			userInfoObj.token = record.result.token;

			if(!!userInfoObj.brokerId) {
				const brokerInfo = await getSingleUser(userInfoObj.brokerId);
				if(brokerInfo) {
                    setSessionBrokerInformation(brokerInfo);
                }
			}
			if(!!userInfoObj.agentId) {
				const agentInfo = await getSingleUser(userInfoObj.agentId);
				if(agentInfo)  {
					setSessionAgentInformation(agentInfo);
				}
			}
			// Store userInfo object in sessionStorage
			setSessionUserInformation(userInfoObj)
			// sessionStorage.setItem('user',JSON.stringify(userInfoObj));

			if(userInfoObj?.roleId !== 4) {
				navigate(`/`);
				return;
			}
			if(!!userInfoObj?.rent) {
				//Store userinfo object in global state
				dispatch(storeLoggedInData(userInfoObj));
				if(!!record?.result?.userSettings) {
					setSessionUserSettings(record?.result?.userSettings)
					// sessionStorage.setItem("userSettings",record?.result?.userSettings);
					dispatch(storeUserSettings(convertSettingToNumber(record?.result?.userSettings)));
				} else {
					const userSettingsPayload = {
						userId: userInfoObj.id,
						...SETTINGS_CONSTANT_OBJECT,
					};
					const settingRecord = await insertSettings(userSettingsPayload);
	
					if (settingRecord?.data) {
						const userSettings = convertSettingToNumber(
							settingRecord?.data
						);
						setSessionUserSettings(userSettings);
						dispatch(storeUserSettings(userSettings));
					} 
				}
				// sessionStorage.setItem('userSettings',JSON.stringify(settings));
				navigate(`/home-purchase/step3`);
				setIsLoading(false);
			} else {
                // Insert Setting Values and dispatch setting values
                const userSettingsPayload = {
                    userId: userInfoObj.id,
                    ...SETTINGS_CONSTANT_OBJECT,
                };
                const settingRecord = await insertSettings(userSettingsPayload);

                if (settingRecord?.data) {
                    const userSettings = convertSettingToNumber(
                        settingRecord?.data
                    );
                    setSessionUserSettings(userSettings);
                    dispatch(storeUserSettings(userSettings));
                } else {
                    showError(settingRecord?.message);
                }
                setIsLoading(false);
                // setTimeout(() => {
                navigate("/home-purchase/step1");
                // }, 1000);
            }
           
        }
		
	};

	return (
		<div className="center-align">
			  {isLoading && <Loading />}
            <Toast ref={toast} className="ToastMessage" />
			<div className="LoginForm">
				<form method="post" onSubmit={handleSubmit}>
					<div className="content">
						<label className="email-label">
							Enter your password
						</label>
						<div className="email-input">
							<span className="p-float-label">
								<Password
									autoFocus
									type="password"
									className="vw-input-password"
									autoComplete="off"
									required={false}
									name="password"
									value={password}
									onChange={(e) =>
										setPassword(e.target.value)
									}
									toggleMask={true}
									feedback={false} 
								/>
								<label>Password *</label>
							</span>
							<Button
								disabled={!!password === false}
								label="Submit"
								className="p-button-raised p-button-warning login-button"
								// onClick={handleSubmit}
							/>
						</div>
						<div>
                            {formSubmitted && !password && (
                                <small className="p-error">
                                    Please enter password.
                                </small>
                            )}
                        </div>
						<Link
							className="create-account"
							to="/session/forgot-password"
						>
							Forgot Password
						</Link>
					</div>
				</form>
			</div>
		</div>
	);
};
export default Step2;
