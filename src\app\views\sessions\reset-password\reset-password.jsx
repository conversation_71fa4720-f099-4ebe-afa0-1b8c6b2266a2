import { But<PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Password } from "primereact/password";
import React, { useState, useRef } from "react";
import { useDispatch } from "react-redux";
import { updatePassword } from "../session.service";
import { useNavigate } from "react-router-dom";
import { Toast } from "primereact/toast";

const ResetPassword = () => {
  const toast = useRef(null);
  const [email, setEmail] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const validatePassword = (password) => {
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[\W_]/.test(password);
    const noConsecutiveAlpha = !/(?:([a-zA-Z])\1)/.test(password);
    const noConsecutiveNumbers = !/(?:([0-9])\1)/.test(password);

    if (!hasUpperCase) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password must contain at least one uppercase letter.",
      });
      return false;
    }
    if (!hasLowerCase) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password must contain at least one lowercase letter.",
      });
      return false;
    }
    if (!hasNumber) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password must contain at least one number.",
      });
      return false;
    }
    if (!hasSpecialChar) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password must contain at least one special character.",
      });
      return false;
    }
    if (!noConsecutiveAlpha) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password cannot have consecutive alphabetic characters.",
      });
      return false;
    }
    if (!noConsecutiveNumbers) {
      toast.current?.show({
        severity: "error",
        summary: "Weak Password",
        detail: "Password cannot have consecutive numbers.",
      });
      return false;
    }

    return true;
  };

  const handleResetPassword = async () => {
    if (!email || !currentPassword || !newPassword || !confirmPassword) {
      toast.current?.show({
        severity: "error",
        summary: "Missing Fields",
        detail: "All fields are required!",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.current?.show({
        severity: "error",
        summary: "Password Mismatch",
        detail: "New password and confirm password do not match.",
      });
      return;
    }

    if (!validatePassword(newPassword)) {
      return;
    }

    try {
      const response = await updatePassword({
        email,
        currentPassword,
        newPassword,
      });
      if (response.success) {
        toast.current?.show({
          severity: "success",
          summary: "Password Reset Successful",
          detail: "You can now log in with your new password.",
        });
        setTimeout(() => navigate("/login"), 2000);
      } else {
        toast.current?.show({
          severity: "error",
          summary: "Reset Failed",
          detail: response.message || "Failed to update password.",
        });
      }
    } catch (err) {
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Something went wrong. Please try again later.",
      });
      console.error("Error resetting password:", err);
    }
  };

  return (
    <div>
      <Toast ref={toast} />
      <div className="flex flex-column align-items-center justify-content-center">
        <div
          style={{
            borderRadius: "56px",
            padding: "0.3rem",
            background:
              "linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 100%)",
          }}
        >
          <div
            className="w-full surface-card py-8 px-5 sm:px-8"
            style={{ borderRadius: "53px" }}
          >
            <div className="text-center mb-5">
              <div className="text-900 text-3xl font-medium mb-3">
                Reset Your Password
              </div>
              <span className="text-600 font-medium">
                Enter your details to continue
              </span>
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-900 text-xl font-medium mb-2"
              >
                Email
              </label>
              <InputText
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                className="w-full md:w-30rem mb-3 p-3"
              />

              <label
                htmlFor="currentPassword"
                className="block text-900 text-xl font-medium mb-2"
              >
                Current Password
              </label>
              <Password
                id="currentPassword"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Current Password"
                toggleMask
                className="w-full mb-3"
                inputClassName="w-full p-3 md:w-30rem"
              />

              <label
                htmlFor="newPassword"
                className="block text-900 text-xl font-medium mb-2"
              >
                New Password
              </label>
              <Password
                id="newPassword"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="New Password"
                toggleMask
                className="w-full mb-3"
                inputClassName="w-full p-3 md:w-30rem"
              />

              <label
                htmlFor="confirmPassword"
                className="block text-900 text-xl font-medium mb-2"
              >
                Confirm New Password
              </label>
              <Password
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm New Password"
                toggleMask
                className="w-full mb-5"
                inputClassName="w-full p-3 md:w-30rem"
              />

              <Button
                label="Reset Password"
                className="w-full p-3 text-xl"
                onClick={handleResetPassword}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
