import { Fragment, useEffect, useRef, useState } from "react";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import { Link, useNavigate } from "react-router-dom";
import { SuccessToastConfig, ErrorToastConfig } from "app/utils/ToastConstants";
import { Loading } from "app/components";
import "../Session.scss";
import { validateEmail } from "app/utils/utility.service";
import { doRegister, fetchCategories } from "../session.service";
import { Password } from "primereact/password";
import { Calendar } from "primereact/calendar";
import { FileUpload } from "primereact/fileupload";
import moment from "moment";
import { InputTextarea } from "primereact/inputtextarea";
import { RadioButton } from "primereact/radiobutton";

const initialState = {
    category: "",
};

const RegisterStepOne = () => {
    const [values, setValues] = useState(initialState);
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const toast = useRef();
    const navigate = useNavigate();
    const dateOfBirthRef = useRef(null);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setValues({ ...values, [name]: value });
    };

    const showSuccess = (message) => {
        toast.current.show(SuccessToastConfig(message));
    };
    const showError = (message) => {
        toast.current.show(ErrorToastConfig(message ? message : "Error"));
    };

    const myUploader = (event, name) => {
        console.log(name, event.files[0]);
        setValues({
            ...values,
            [name]: event.files[0],
            [`${name}Name`]: event.files[0].name,
        });
    };

    const validateInput = () => {
        if (
            !values.email ||
            !values.firstname ||
            !values.middlename ||
            !values.password ||
            !values.mobile_number ||
            !values.qualification ||
            !values.certificate_status ||
            !values.date_of_birth ||
            !values.system_of_medicine ||
            !values.name_state_board ||
            !values.awarding_body ||
            !values.state_registration_number ||
            !values.permanent_address ||
            !values.state_registration_date
        ) {
            return false;
        }

        if (!validateEmail(values.email)) {
            return false;
        }

        if (isNaN(values.state_registration_number)) {
            return false;
        }
        if (isNaN(values.mobile_number)) {
            return false;
        } else {
            if (values.mobile_number.length !== 10) {
                return false;
            }
        }
        if (isNaN(values.state_registration_number)) {
            return false;
        }
        if (values?.certificate_status === "Old Certificate") {
            if (!values?.central_registration_number) {
                return false;
            } else {
                if (isNaN(values.central_registration_number)) {
                    return false;
                }
            }
        }
        // if (!values?.dateOfBirthImage?.name) {
        //     return false;
        // }
        // if (!values?.addressProofImage?.name) {
        //     return false;
        // }
        // if (!values?.aadharProof?.name) {
        //     return false;
        // }
        return true;
    };

    const clearImage = () => {
        console.log("da", dateOfBirthRef);
        dateOfBirthRef.current.value = null;
        console.log("da", dateOfBirthRef);
    };

    const cardFooter = (
        <span className="ml-3">
            <Link
                to={`/session/signup/step1?category=${values?.category}`}
                style={{ textDecoration: "none" }}
            >
                <Button
                    // onClick={handleSubmit}
                    label="Next"
                    className="p-button-raised p-button-warning p-button-lg mr-4"
                    disabled={!values?.category}
                />
            </Link>
            <Link to="/session/signin/step1" style={{ textDecoration: "none" }}>
                <Button
                    type="button"
                    label="Cancel"
                    className="p-button-raised p-button-lg"
                />
            </Link>
        </span>
    );

    return (
        <div className="center-align">
            {isLoading && <Loading />}
            <Toast ref={toast} className="ToastMessage" />
            <div className="RegisterForm">
                <Card
                    title="Create your account - Step One Choose Category"
                    subTitle="You can only register if you are citizen of India"
                    footer={cardFooter}
                >
                    <div className="grid">
                        <div className="col-12 md:col-8 lg:col-8 content pr-4 pl-4">
                            <div className="card">
                                <div className="flex flex-wrap gap-3 mb-3">
                                    <div className="flex align-items-center">
                                        <RadioButton
                                            inputId="category_doctors"
                                            name="category"
                                            value="Naturopathy Doctors"
                                            onChange={handleInputChange}
                                            checked={
                                                values?.category ===
                                                "Naturopathy Doctors"
                                            }
                                        />
                                        <label
                                            htmlFor="category_doctors"
                                            className="ml-2"
                                        >
                                            Naturopathy Doctors
                                        </label>
                                    </div>
                                </div>
                                <div className="flex align-items-center mb-3">
                                    <RadioButton
                                        inputId="category_therapist"
                                        name="category"
                                        value="Naturopathy Therapist"
                                        onChange={handleInputChange}
                                        checked={
                                            values?.category ===
                                            "Naturopathy Therapist"
                                        }
                                    />
                                    <label
                                        htmlFor="category_therapist"
                                        className="ml-2"
                                    >
                                        Naturopathy Therapist
                                    </label>
                                </div>
                                <div className="flex align-items-center">
                                    <RadioButton
                                        inputId="category_health"
                                        name="category"
                                        value="Naturopathy Health Promoters"
                                        onChange={handleInputChange}
                                        checked={
                                            values?.category ===
                                            "Naturopathy Health Promoters"
                                        }
                                    />
                                    <label
                                        htmlFor="category_health"
                                        className="ml-2"
                                    >
                                        Naturopathy Health Promoters
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 md:col-4 lg:col-4 content pr-4 pl-4">
                            <a
                                href="/Instructions and Eligibility criteria for central registration of bnys graduates.pdf"
                                download
                            >
                                Click here to download Instructions for
                                Practitioner’s Registration for Central
                                Registration
                            </a>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default RegisterStepOne;
