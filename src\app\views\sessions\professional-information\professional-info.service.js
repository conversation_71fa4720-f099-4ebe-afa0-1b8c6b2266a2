import RestClient from "RestClient";
const BASE_URL = process.env.REACT_APP_API_BASE_URL;

const restClient = new RestClient();

export const getSpokenLanguages = async () => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/languages`
  ); //done

  return result?.data?.data;
};

export const getAllStates = async () => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/states`
  ); //done
  return result?.data?.data;
};

export const getAllDistricts = async (stateId) => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/district/${stateId}`
  ); //done
  return result?.data?.data;
};

// export const getAllCategories = async () => {
//   const result = await restClient.get(`${BASE_URL}/personal-information/categories`);
//   return result?.data?.data
// };

export const getAllCategories = async () => {
  return [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "<PERSON>" },
  ];
};

export const getAllCouncils = async () => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/medical-councils`
  ); //done
  return result?.data?.data;
};

export const getAllCourses = async () => {
  const result = await restClient.post(
    `${BASE_URL}/personal-information/courses`,
    []
  ); //done
  return result?.data?.data;
};

export const getAllColleges = async (stateId) => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/colleges/${stateId}`
  ); //done
  return result?.data?.data;
};

export const getAllUniversities = async (collegeId) => {
  const result = await restClient.get(
    `${BASE_URL}/personal-information/universites/${collegeId}`
  ); //done
  return result?.data?.data;
};
