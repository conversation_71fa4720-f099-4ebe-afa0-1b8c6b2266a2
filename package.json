{"name": "nrb", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@react-pdf/renderer": "^3.1.12", "@reduxjs/toolkit": "^1.8.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^0.26.1", "chart.js": "^4.2.1", "chartjs-plugin-datalabels": "^2.2.0", "dayjs": "^1.11.13", "env-cmd": "^10.1.0", "formik": "^2.4.6", "moment": "^2.29.3", "normalize.css": "^8.0.1", "primeflex": "^3.1.3", "primeicons": "^5.0.0", "primereact": "^7.2.1", "react": "^17.0.2", "react-barcode": "^1.4.6", "react-chartjs-2": "^5.2.0", "react-dom": "^17.0.2", "react-redux": "^7.2.6", "react-router-dom": "^6.2.2", "react-scripts": "5.0.0", "react-to-print": "^2.14.7", "react-transition-group": "^4.4.2", "sass": "^1.49.9", "yup": "^1.6.1"}, "scripts": {"start": "env-cmd -f env/.env react-scripts start", "build": "env-cmd -f env/.env react-scripts build", "build:dev": "env-cmd -f env/.env.fat react-scripts build", "build:prod": "env-cmd -f env/.env.prod react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}