import { Loading } from "app/components";
import { ErrorToastConfig,SuccessToastConfig } from "app/utils/ToastConstants";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Toast } from "primereact/toast";
import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import "../Session.scss";
import { requestResetPassword } from "../session.service";
const ForgotPassword = () => {
	const [email, setEmail] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const toast = useRef();
	const navigate = useNavigate();
	const showError = (message) => {
		toast.current.show(ErrorToastConfig(message ? message : "Error"));
	};

	const showSuccess = (message) => {
		toast.current.show(SuccessToastConfig(message));
	};

    const handleClick = async (e) => {
		e.preventDefault();
        const payload = {
			email
        }
		setIsLoading(true);
		const record = await requestResetPassword(payload);
		console.log(record);
		if(!record.status) {
			setIsLoading(false);
			showError(record?.message);
			return;
		} else {
            setIsLoading(false);
            showSuccess("Reset password link sent to your email.");
			setTimeout(()=> {
				navigate("/session/signin/step1")
			}, 2000)
        }
    }
    
	return (
        <div className="center-align">
            {isLoading && <Loading />}
            <Toast ref={toast} className="ToastMessage" />
            <div className="LoginForm ForgotPasswordForm">
                <h2>Forgot Password</h2>
                <form onSubmit={handleClick} method="POST">
                    <div className="content">
                        <div className="email-input">
                            <span className="p-float-label">
                                <InputText
                                    autoFocus
                                    className="vw-input"
                                    autoComplete="off"
                                    required={true}
                                    name="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                                <label>Email Address *</label>
                            </span>
                        </div>
                        <div className="mt-4 flex">
                            <Button
                                label="Cancel"
                                className="p-button-raised p-button-lg login-button mr-2"
                                onClick={() =>
                                    navigate("/session/signin/step1")
                                }
								type="button"
                            />
                            <Button
                                disabled={!email || email === ""}
                                label="Send New Password"
                                className="p-button-raised p-button-warning p-button-lg login-button"
                            />
                        </div>
                    </div>
                </form>
            </div>
            {/* <div>Step 1</div>
            <Button
                onClick={handleClick}
                label="Step 2"
                className="p-button-raised p-button-warning p-button-lg"
            /> */}
        </div>
    );
};

export default ForgotPassword;
