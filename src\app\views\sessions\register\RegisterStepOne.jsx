import { Fragment, useEffect, useRef, useState } from "react";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { SuccessToastConfig, ErrorToastConfig } from "app/utils/ToastConstants";
import { Loading } from "app/components";
import "../Session.scss";
import { validateEmail } from "app/utils/utility.service";
import { doRegister, fetchCategories } from "../session.service";
import { Password } from "primereact/password";
import { Calendar } from "primereact/calendar";
import { FileUpload } from "primereact/fileupload";
import moment from "moment";
import { InputTextarea } from "primereact/inputtextarea";
import { RadioButton } from "primereact/radiobutton";
import { SYSTEM_OF_MEDICINE_LIST } from "app/utils/Constants";
import { QUALIFICATION_LIST } from "app/utils/Constants";
import {
  getAllDistricts,
  getAllStates,
} from "../professional-information/professional-info.service";

const initialState = {
  firstname: "",
  middlename: "",
  lastname: "",
  mobile_number: "",
  email: "",
  password: "",
  date_of_birth: "",
  certificate_status: "",
  qualification: "",
  name_state_board: "",
  system_of_medicine: "",
  awarding_body: "",
  state_registration_number: "",
  state_registration_date: null,
  old_central_registration_number: null,
  contact_address: "",
  permanent_address: "",
  status: "",
};

const Register = () => {
  const [values, setValues] = useState(initialState);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useRef();
  const navigate = useNavigate();
  const dateOfBirthRef = useRef(null);

  //dropdowns
  const [states, setStates] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [selectedState, setSelectedState] = useState(null);
  const [selectedDistrict, setSelectedDistrict] = useState(null);

  const [searchParams] = useSearchParams();
  const category = searchParams.get("category");

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const response = await getAllStates();
        setStates(response);
      } catch (error) {
        console.error("Error fetching states:", error);
      }
    };
    fetchStates();
  }, []);

  const handleStateChange = async (e) => {
    const state = e.value;
    setSelectedState(state);
    setValues({ ...values, state_id: state.id });

    try {
      const response = await getAllDistricts(state?.id);
      setDistricts(response);
    } catch (error) {
      console.error("Error fetching districts:", error);
    }
  };

  const handleDistrictChange = (e) => {
    const district = e.value;
    setSelectedDistrict(district);
    setValues({ ...values, district_id: district.id });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setValues({ ...values, [name]: value });
  };

  const showSuccess = (message) => {
    toast.current.show(SuccessToastConfig(message));
  };
  const showError = (message) => {
    toast.current.show(ErrorToastConfig(message ? message : "Error"));
  };

  const myUploader = (event, name) => {
    console.log(name, event.files[0]);
    setValues({
      ...values,
      [name]: event.files[0],
      [`${name}Name`]: event.files[0].name,
    });
  };

  const validateInput = () => {
    if (
      !values.email ||
      //   !values.firstname ||
      //   !values.middlename ||
      !values.password ||
      !values.mobile_number ||
      // !values.qualification ||
      !values.certificate_status ||
      //   !values.date_of_birth ||
      // !values.system_of_medicine ||
      // !values.awarding_body ||
      !values.state_id ||
      !values.district_id
      //   !values.permanent_address
    ) {
      return false;
    }

    if (!validateEmail(values.email)) {
      return false;
    }

    // if (isNaN(values.state_registration_number)) {
    //     return false;
    // }
    if (isNaN(values.mobile_number)) {
      return false;
    } else {
      if (values.mobile_number.length !== 10) {
        return false;
      }
    }
    // if (isNaN(values.state_registration_number)) {
    //     return false;
    // }
    if (values?.certificate_status === "Old Certificate") {
      if (!values?.old_central_registration_number) {
        return false;
      } else {
        if (isNaN(values.old_central_registration_number)) {
          return false;
        }
      }
    }
    // if (!values?.dateOfBirthImage?.name) {
    //     return false;
    // }
    // if (!values?.addressProofImage?.name) {
    //     return false;
    // }
    // if (!values?.aadharProof?.name) {
    //     return false;
    // }
    return true;
  };

  const validatePassword = () => {
    if (!values.password) {
      showError("Password is required.");
      return false;
    }

    const hasUpperCase = /[A-Z]/.test(values.password);
    const hasLowerCase = /[a-z]/.test(values.password);
    const hasNumber = /[0-9]/.test(values.password);
    const hasSpecialChar = /[\W_]/.test(values.password);
    const noConsecutiveAlpha = !/(?:([a-zA-Z])\1)/.test(values.password);
    const noConsecutiveNumbers = !/(?:([0-9])\1)/.test(values.password);

    if (!hasUpperCase) {
      showError("Password must contain at least one uppercase letter.");
      return false;
    }
    if (!hasLowerCase) {
      showError("Password must contain at least one lowercase letter.");
      return false;
    }
    if (!hasNumber) {
      showError("Password must contain at least one number.");
      return false;
    }
    if (!hasSpecialChar) {
      showError("Password must contain at least one special character.");
      return false;
    }
    if (!noConsecutiveAlpha) {
      showError("Password cannot have consecutive identical letters.");
      return false;
    }
    if (!noConsecutiveNumbers) {
      showError("Password cannot have consecutive identical numbers.");
      return false;
    }

    return true;
  };

  // Assuming this function is part of the registration process
  const handleRegister = () => {
    if (!validateInput()) {
      return;
    }

    // Proceed with registration logic
  };

  function processText(inputText) {
    // Split the text by commas
    var parts = inputText.split(",");

    // Remove leading and trailing spaces from each part and join with a comma and space
    var resultText = parts
      .map(function (part) {
        return part.trim();
      })
      .join(", ");

    return resultText;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    // if (!!values?.dateOfBirthImage?.name) {
    //     formData.append(
    //         "userfiles",
    //         values.dateOfBirthImage,
    //         `DOB_${values?.dateOfBirthImage?.name}`
    //     );
    // } else {
    //     alert("Please upload Date of Birth PDF");
    //     return;
    // }
    // if (!!values?.addressProofImage?.name) {
    //     formData.append(
    //         "userfiles",
    //         values.addressProofImage,
    //         `ADDRESSPROOF_${values?.addressProofImage?.name}`
    //     );
    // } else {
    //     alert("Please upload Address Proof PDF");
    //     return;
    // }

    // if (!!values?.aadharProof?.name) {
    //     formData.append(
    //         "userfiles",
    //         values.aadharProof,
    //         `AADHARPROOF_${values?.aadharProof?.name}`
    //     );
    // } else {
    //     alert("Please upload Aadhar Proof PDF");
    //     return;
    // }

    // delete values.dateOfBirthImage;
    // delete values.addressProofImage;
    // delete values.aadharProof;
    // for (const key of Object.keys(values)) {
    //     formData.append(key, `${values[key]}`);
    // }
    setFormSubmitted(true);
    if (!validateInput()) {
      return false;
    }
    if (!validatePassword()) {
      return;
    }
    // if (!validateInput() || !values.state_id || !values.district_id) {
    //   return false;
    // }

    values.contact_address = processText(values.contact_address);
    values.permanent_address = processText(values.permanent_address);
    setIsLoading(true);
    values.status = "Active";
    values.category = category;
    const record = await doRegister(values);
    if (record.status === false) {
      setIsLoading(false);
      showError(record?.message);
    } else {
      showSuccess("New account created successfully");
      // insert settings value
      setIsLoading(false);
      setTimeout(() => {
        navigate("/session/signin/step1");
      }, 1000);
    }
  };

  const clearImage = () => {
    console.log("da", dateOfBirthRef);
    dateOfBirthRef.current.value = null;
    console.log("da", dateOfBirthRef);
  };

  const cardFooter = (
    <span className="ml-3">
      <Button
        onClick={handleSubmit}
        label="Create My Account"
        className="p-button-raised p-button-warning p-button-lg mr-4"
        disabled={!values?.certificate_status}
      />
      <Link to="/session/signin/step1" style={{ textDecoration: "none" }}>
        <Button
          type="button"
          label="Cancel"
          className="p-button-raised p-button-lg"
        />
      </Link>
    </span>
  );

  const cardNotFoundFooter = (
    <span className="">
      <Link to="/session/signup" style={{ textDecoration: "none" }}>
        <Button
          type="button"
          label="Go Back"
          className="p-button-raised p-button-lg"
        />
      </Link>
    </span>
  );

  const renderNotFound = () => {
    return (
      <Card
        title={`The category ${category} is not yet enabled.`}
        footer={cardNotFoundFooter}
      >
        {/* <h1>{`We are still working on ${category}`}</h1> */}
      </Card>
    );
  };

  return (
    <div className="center-align">
      {isLoading && <Loading />}
      <Toast ref={toast} className="ToastMessage" />
      <div className="RegisterForm">
        {category === "Naturopathy Doctors" ? (
          <form method="post" onSubmit={handleSubmit}>
            <Card
              title="Create your account"
              subTitle="You can only register if you are citizen of India"
              footer={cardFooter}
            >
              <div className="grid">
                <div className="col-12 md:col-8 lg:col-8 content pr-4 pl-4">
                  <div className="card">
                    <div className="flex flex-wrap gap-3 mb-3">
                      <div className="flex align-items-center">
                        <RadioButton
                          inputId="oldCertificate"
                          name="certificate_status"
                          value="Old Certificate"
                          onChange={handleInputChange}
                          checked={
                            values?.certificate_status === "Old Certificate"
                          }
                        />
                        <label htmlFor="oldCertificate" className="ml-2">
                          Already Registered with CCRYN?
                        </label>
                      </div>
                    </div>
                    <div className="flex align-items-center">
                      <RadioButton
                        inputId="newCertificate"
                        name="certificate_status"
                        value="New Certificate"
                        onChange={handleInputChange}
                        checked={
                          values?.certificate_status === "New Certificate"
                        }
                      />
                      <label htmlFor="newCertificate" className="ml-2">
                        New Registration
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              {values?.certificate_status && (
                <Fragment>
                  {values?.certificate_status === "Old Certificate" && (
                    <h2 className="ml-3">
                      <strong>Note:</strong> The Central Registration
                      certificate will be issued only on receipt of the CCRYN
                      certificate
                    </h2>
                  )}
                  {/* <div className="grid">
                    <div className="col-12 md:col-3 lg:col content pr-4 pl-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            autoFocus
                            className="vw-input"
                            autoComplete="off"
                            disabled={true}
                            name="firstname"
                            value={"Dr"}
                          />
                          <label>Prefix *</label>
                        </span>
                      </div>
                    </div>
                    <div className="col-12 md:col-3 lg:col content pr-4 pl-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            autoFocus
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="firstname"
                            value={values?.firstname}
                            onChange={handleInputChange}
                          />
                          <label>Name appeared on Certificate *</label>
                          {formSubmitted && !values?.firstname && (
                            <small className="p-error">Please enter name</small>
                          )}
                        </span>
                      </div>
                    </div>
                    <div className="col-12 md:col-3 lg:col content pl-4 pr-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="middlename"
                            value={values?.middlename}
                            onChange={handleInputChange}
                          />
                          <label>Father's Name *</label>
                        </span>
                        {formSubmitted && !values?.middlename && (
                          <small className="p-error">
                            Please enter father's name
                          </small>
                        )}
                      </div>
                    </div>
                    <div className="col-12 md:col-3 lg:col content pl-4 pr-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            className="vw-input"
                            autoComplete="off"
                            required={false}
                            name="lastname"
                            value={values?.lastname}
                            onChange={handleInputChange}
                          />
                          <label>Surname</label>
                        </span>
                      </div>
                    </div>
                  </div> */}
                  <div className="grid">
                    <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="mobile_number"
                            value={values?.mobile_number}
                            onChange={handleInputChange}
                            maxLength={10}
                          />
                          <label>Mobile No. *</label>
                        </span>
                        <small className="p-info">
                          Please enter the mobile number linked with your
                          Aadhaar.
                        </small>
                        {formSubmitted && !values?.mobile_number && (
                          <small className="p-error">
                            Please enter mobile number
                          </small>
                        )}
                        {formSubmitted && isNaN(values?.mobile_number) && (
                          <small className="p-error">
                            Mobile Number must be in numeric format
                          </small>
                        )}
                        {formSubmitted &&
                          !isNaN(values?.mobile_number.length) &&
                          values?.mobile_number.length != 10 && (
                            <small className="p-error">
                              Mobile number must be of 10 digits
                            </small>
                          )}
                      </div>
                    </div>
                    <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                      <div>
                        <span className="p-float-label">
                          <InputText
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="email"
                            value={values?.email}
                            onChange={handleInputChange}
                          />
                          <label>Email Address *</label>
                        </span>
                        {formSubmitted && !values?.email && (
                          <small className="p-error">
                            Please enter email address.
                          </small>
                        )}
                        {formSubmitted &&
                          values?.email &&
                          !validateEmail(values?.email) && (
                            <small className="p-error">
                              Invalid Email Address
                            </small>
                          )}
                      </div>
                    </div>
                    <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
                      <div>
                        <span className="p-float-label">
                          <Password
                            toggleMask
                            feedback={false}
                            type="password"
                            className="vw-input-password"
                            autoComplete="off"
                            required={true}
                            name="password"
                            value={values?.password}
                            onChange={handleInputChange}
                          />
                          <label>Password *</label>
                        </span>
                        {formSubmitted && !values?.password && (
                          <small className="p-error">
                            Please enter password
                          </small>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* <div className="grid">
                    <div className="col-12 md:col-4 lg:col-4 content pr-4 pl-4">
                      <div>
                        <span className="p-float-label vw-calendar">
                          <Calendar
                            style={{
                              width: "100%",
                            }}
                            inputId="date_of_birth"
                            value={new Date(values?.date_of_birth)}
                            name="date_of_birth"
                            onChange={handleInputChange}
                            dateFormat="dd-mm-yy"
                            monthNavigator
                            yearNavigator
                            yearRange={`1924:${moment().format("YYYY")}`}
                            showIcon
                          />

                          <label htmlFor="birth_date">Date Of Birth *</label>
                        </span>
                        {formSubmitted && !values?.date_of_birth && (
                          <small className="p-error">
                            Please enter Date Of Birth
                          </small>
                        )}
                      </div>
                    </div>
                  </div> */}
                  <div className="grid">
                    <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                      <span className="p-float-label">
                        <Dropdown
                          className="vw-dropdown"
                          value={selectedState}
                          options={states}
                          optionLabel="name"
                          onChange={handleStateChange}
                        />
                        <label>State *</label>
                      </span>
                      {formSubmitted && !values.state_id && (
                        <small className="p-error">Please select a state</small>
                      )}
                    </div>

                    <div className="col-12 md:col-4 lg:col content pr-4 pl-4">
                      <span className="p-float-label">
                        <Dropdown
                          className="vw-dropdown"
                          value={selectedDistrict}
                          options={districts}
                          optionLabel="districtName"
                          onChange={handleDistrictChange}
                          disabled={!selectedState}
                        />
                        <label>District *</label>
                      </span>
                      {formSubmitted && !values.district_id && (
                        <small className="p-error">
                          Please select a district
                        </small>
                      )}
                    </div>
                  </div>

                  <div className="grid">
                    <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
                      <div>
                        <span className="p-float-label">
                          <InputTextarea
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="contact_address"
                            value={values?.contact_address}
                            onChange={handleInputChange}
                            maxLength={90}
                          />
                          <label>Contact Address *</label>
                        </span>
                        {formSubmitted && !values?.contact_address && (
                          <small className="p-error">
                            Please enter Contact Address
                          </small>
                        )}
                      </div>
                    </div>

                    {/* <div className="col-12 md:col-4 lg:col content pl-4 pr-4">
                      <div>
                        <span className="p-float-label">
                          <InputTextarea
                            className="vw-input"
                            autoComplete="off"
                            required={true}
                            name="permanent_address"
                            value={values?.permanent_address}
                            onChange={handleInputChange}
                            maxLength={90}
                          />
                          <label>Permanent Address *</label>
                        </span>
                        {formSubmitted && !values?.permanent_address && (
                          <small className="p-error">
                            Please enter Permanent Address
                          </small>
                        )}
                      </div>
                    </div> */}
                  </div>
                </Fragment>
              )}
            </Card>
          </form>
        ) : (
          renderNotFound()
        )}
      </div>
    </div>
  );
};

export default Register;
