import { Card } from "primereact/card";
import React, { Fragment, useEffect, useRef, useState } from "react";
import "../Session.scss";
import CertificateDetails from "./CertificateDetails";
import CertificateHeader from "./CertificateHeader";
import TrueCopyText from "./TrueCopyText";
import { Button } from "primereact/button";
import { useReactToPrint } from "react-to-print";
import { getSessionUserInformation } from "app/utils/utility.service";
import { getSingleUser } from "../my-profile/my-profile.service";
import { Loading } from "app/components";
import moment from "moment";
import { getUserDocumentList } from "../my-documents/my-document.service";

export const ComponentToPrint = React.forwardRef((props, ref) => {
    const { userValues, documentSignature, documentProfileImage } = props;
    const qrValue = `https://app.nrb.net.in/%23/doVerification/verifyQR?qrCode=${userValues?.central_registration_number_pre}-${userValues?.central_registration_number}`;
    return (
        <div ref={ref}>
            <style>
                {`
               @media print {
                @page { size: A4; }
              }
                `}
            </style>
            <div className="center-align CertificateTemplate">
                <div
                    style={{
                        width: "750px",
                        position: "relative",
                        marginLeft: "2rem",
                    }}
                >
                    <div style={{ position: "absolute", top: 0, left: 0 }}>
                        <img
                            style={{ height: "50%", width: "100%" }}
                            src={"/golden-vintage.webp"}
                        />
                    </div>
                    <Card className="cardCertificate">
                        <CertificateHeader userValues={userValues} />
                        <CertificateDetails userValues={userValues} />
                        <TrueCopyText />
                        <div className="grid">
                            <div className="col-6">
                                {!!documentProfileImage?.fileName && (
                                <img
                                    style={{ height: "80px", width:"80px" }}
                                    src={`${process.env.REACT_APP_API_BASE_URL}/fetchDocumentsPDF/${documentProfileImage?.fileName}`}
                                />
                                )}
                            </div>
                            <div className="col-6 text-center">
                                <img className="ml-5" src={`https://image-charts.com/chart?chs=50x50&cht=qr&chl=${qrValue}&choe=UTF-8`} />
                            </div>
                        </div>
                        <div className="grid flex align-items-center">
                            <div className="col-4">
                                {!!documentSignature?.fileName && (
                                <img
                                    style={{ height: "80px", width:"120px" }}
                                    src={`${process.env.REACT_APP_API_BASE_URL}/fetchDocumentsPDF/${documentSignature?.fileName}`}
                                />
                                )}
                                <div style={{ color: "#040000" }}>
                                    Practitioner's Signature
                                </div>
                                <div className="mt-3" style={{ color: "#040000" }}>
                                        <div>
                                        Issue Date:{" "}
                                        {!!userValues?.approved_date
                                            ? `${moment(
                                                  userValues?.approved_date
                                              ).format("DD/MM/YYYY")}`
                                            : "-"}
                                            </div>
                                            <div>
                                        Valid Upto:{" "}
                                        {!!userValues?.approved_date
                                            ? moment(
                                                  userValues?.approved_date
                                              ).add(5,'years').format("MM/YYYY")
                                            : "-"}
                                            </div>
                                    
                                </div>
                            </div>
                            <div className="col-4"></div>
                            <div className="col-4 text-center">
                                <img
                                    style={{ height: "80px" }}
                                    src="/officialsignature.png"
                                />

                                <div className="">
                                    <div style={{ color: "#040000" }}>
                                        मुख्य कार्यकारी अधिकारी
                                    </div>
                                    <div style={{ color: "#040000" }}>
                                        Chief Executive Officer (CEO){" "}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>
                <div className="page-break">
                    <Card className="cardCertificate">
                    <h2 className="mb-0">For Further Details</h2>
<h4 className="mb-0 mt-0">Phone No.: +91 020 26059131</h4>
<h4 className="mb-0 mt-0">Website: www.nrb.net.in</h4>
<h4 className="mb-0 mt-0">E-mail ID: <EMAIL></h4>
                    </Card>
                    
                </div>
            </div>

            <div className="printSeparation"></div>
        </div>
    );
});

const Certificate = () => {
    const componentRef = useRef(null);

    const [isLoading, setIsLoading] = useState(false);
    const [values, setValues] = useState(null);
    const sessionValues = getSessionUserInformation();
    const [documentProfileImage, setDocumentProfileImage] = useState(null);
    const [documentSignature, setDocumentSignature] = useState(null);

    const fetchUserDocumentList = async () => {
        // setIsLoading(true);

        let list = await getUserDocumentList(sessionValues?.id);
        if (list.length > 0) {
            const findDocumentProfilePic = list.find((li)=>li.documentName.includes("Photograph with white background"));
            const findDocumentSignature = list.find((li)=>li.documentName.includes("Specimen Signature"));
            if(findDocumentProfilePic) {
                setDocumentProfileImage(findDocumentProfilePic);
            }
            if(findDocumentSignature) {
                setDocumentSignature(findDocumentSignature);
            }
        } else {
            setDocumentSignature(null);
            setDocumentProfileImage(null);
        }
        return;
    };
    useEffect(() => {
        const fetchUser = async () => {
            setIsLoading(true);
            let record = await getSingleUser(sessionValues.id);
            setValues(record);
            setIsLoading(false);
        };
        if (!!sessionValues.id) {
            fetchUser();
            fetchUserDocumentList();
        }
    }, []);

    const handlePrint = useReactToPrint({
        content: () => componentRef.current,
    });

    return (
        <Fragment>
            {isLoading && <Loading />}
            <ComponentToPrint userValues={values} documentProfileImage={documentProfileImage}  documentSignature={documentSignature} ref={componentRef} />
            <Button
                className="p-button-raised p-button-warning mr-2"
                onClick={handlePrint}
            >
                Download Certificate
            </Button>
        </Fragment>
    );
};

export default Certificate;
