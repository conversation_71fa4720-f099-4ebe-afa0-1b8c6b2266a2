import { Button } from "primereact/button";
import { doPayment, verifyPayment } from "../session.service";
import { getSessionUserInformation } from "app/utils/utility.service";
import { Fragment, useState } from "react";
import { Loading } from "app/components";

const Payments = ({ checkChanges,setCheckChanges, currentUserRecord}) => {
    const sessionValues = getSessionUserInformation();
    const [isLoading, setIsLoading] = useState(false);

    const handlePayment = async() => {
        setIsLoading(true);
        try {
            const payload = {
                amount: 2500,
                user_id: sessionValues?.id, 
            }
            const record = await doPayment(payload);
            if(record.data?.status === true) {
			    initPayment(record.data?.order);
            }
        } catch(error) {
            setIsLoading(false);
            console.log(error);
        }

    }


    const initPayment = async (data) => {

        setIsLoading(true);
        try {
            // Make a request to the Node.js server to initiate payment
            const formData = {
                merchant_id: data?.merchant_id,
                order_id: data?.order_id,
                amount: 2500,
                currency: data?.currency, // Change accordingly
                redirect_url: data?.redirect_url, // Redirect URL after payment
                cancel_url: data?.cancel_url, // Redirect URL if payment is canceled
                language: data?.language, // Change accordingly,
                billing_name:sessionValues?.firstname,
                billing_address:sessionValues?.permanent_address,
                billing_country:"India",
                billing_tel:sessionValues?.mobile_number,
                billing_email:sessionValues?.email
            };
            const urlEncodedString = Object.keys(formData)
                .map(
                    (key) =>
                        `${encodeURIComponent(key)}=${encodeURIComponent(
                            formData[key]
                        )}`
                )
                .join("&");

                try {

                    setIsLoading(true);
                    const response = await fetch(
                        `${process.env.REACT_APP_API_BASE_URL}/payments/initPaymentRequest`,
                        {
                            method: "POST",
                            body: urlEncodedString,
                        }
                    );
        
                    const ccavFormData = await response.json();
        
                    if (ccavFormData.error) {
                        // Handle error
                        console.error(ccavFormData.error);
                        return;
                    }
                    const form = document.createElement("form");
                    form.method = "post";
                    form.action =
                        "https://secure.ccavenue.com/transaction/transaction.do?command=initiateTransaction";

                    for (const key in ccavFormData) {
                        const input = document.createElement("input");
                        input.type = "hidden";
                        input.name = key;
                        input.value = ccavFormData[key];
                        form.appendChild(input);
                    }

                    document.body.appendChild(form);
                    form.submit();
                    // Redirect user to CCAvenue payment page
                } catch (error) {

                    setIsLoading(false);
                    console.error(error);
                }

            return;
            // Dynamically create a form and submit it to CCAvenue

            // const form = document.createElement("form");
            // form.method = "post";
            // form.action =
            //     "https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction";

            // for (const key in ccavFormData) {
            //     const input = document.createElement("input");
            //     input.type = "hidden";
            //     input.name = key;
            //     input.value = ccavFormData[key];
            //     form.appendChild(input);
            // }

            // document.body.appendChild(form);
            // form.submit();
        } catch (error) {

        setIsLoading(false);
            console.error("Error initiating payment:", error);
        }
    };

    

    return (
        <div>
                    {isLoading && <Loading />}

            {!currentUserRecord?.payment_id && (
                <div className="flex justify-content-center">
                    <div className="text-center">
                        <h1>Fees to generate a new certificate is Rs. 2500. Please click pay button to do the payment.</h1>
                        <Button className="p-button p-button-lg p-button-success" onClick={handlePayment} label="Pay Now" />
                    </div>
                </div>
            )}
            {!!currentUserRecord?.payment_id && currentUserRecord?.profile_status !== "Approved" && (
                <h1 className="text-center">Thanks for the payment, you will receive an email once all your details are verified.</h1>
            )}

            {!!currentUserRecord?.payment_id && currentUserRecord?.profile_status === "Approved" && (
                <h1 className="text-center">Your Profile has been approved. You can download your certificate from Certificate Tab.</h1>
            )}
            {!!currentUserRecord?.payment_id && (currentUserRecord?.profile_status === "Rejected" || currentUserRecord?.profile_status === "Pending For Clarification") && (
                <h1 className="text-center">Your Profile has been {currentUserRecord?.profile_status}. Please check your email for more information.</h1>
            )}
        </div>
    )
}

export default Payments;