import { useEffect, useState } from "react";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Toast } from "primereact/toast";
import {
    checkHPIDExist,
    checkMobileNumberSameAsAAdhar,
    createHPRID,
    generateMobileOtp,
    generateOtp,
    getUserData,
    getuserNameSuggestions,
    updateUsersData,
    verifyMobileOtp,
    verifyOtp,
} from "./aadharverificatio.service";
import { Dropdown } from "primereact/dropdown";
import { formatDateForMySQL } from "app/utils/utility.service";

const AadhaarVerification = ({ userId, setActiveIndex }) => {
    const [aadhaarNumber, setAadhaarNumber] = useState("");
    const [aadhaarNumberSubmitButton, setAadhaarNumberSubmitButton] =
        useState(true);
    const [txnId, settxnId] = useState(null);
    const [otp, setOtp] = useState("");
    const [mobileOtp, setMobileOtp] = useState("");
    const [mobileNumber, SetMobileNumber] = useState("");
    const [showOtpField, setShowOtpField] = useState(false);
    const [showMobileOtpField, SetshowMobileOtpField] = useState(false);
    const [showMobileNumberField, setShowMobileNumberField] = useState(false);
    const [showUserNameSubmit, setshowUserNameSubmit] = useState(false);
    const [loadingUserNameSubmit, SetLoadingUserNameSubmit] = useState(false);
    const [hprIdExist, sethprIdExist] = useState(null);

    const [toast, setToast] = useState(null);
    const [isOtpLoading, setOtpLoading] = useState(false);
    const [isOtpVerifyLoading, setOtpVerifyLoading] = useState(false);
    const [isMobileOtpVerifyLoading, setMobileOtpVerifyLoading] =
        useState(false);
    const [isOtpGenerateLoading, setisOtpGenerateLoading] = useState(false);
    const [selectedUsername, setSelectedUsername] = useState("");
    const [usernameOptions, setuserNameOptions] = useState([]);

    //dynamic user data
    const [user, setUser] = useState(null);

    useEffect(async () => {
        try {
            const response = await getUserData(userId);
            setUser(response);
        } catch (error) {
            console.error("error fetching user details", error);
            toast?.show({
                severity: "error",
                summary: "Get Error",
                detail: "An error occurred. Please try again later!",
            });
        }
    }, [userId, txnId, mobileNumber, selectedUsername]);

    const validateAadhaar = (number) => {
        return /^\d{12}$/.test(number);
    };

    const fetchUserNameSuggestions = async () => {
        try {
            const userNames = await getuserNameSuggestions(txnId);
            setuserNameOptions(userNames);
            setshowUserNameSubmit(true);
            toast?.show({
                severity: "success",
                summary: "user names fetched successfully",
                detail: "User names related to account has been fetched select one of them",
            });
        } catch (error) {
            console.error("Something went wrong", error);
            toast?.show({
                severity: "error",
                summary: "Something went wrong",
                detail: "Please try again later!",
            });
        }
    };

    const handleAadhaarSubmit = async () => {
        if (!validateAadhaar(aadhaarNumber)) {
            toast?.show({
                severity: "error",
                summary: "Invalid Aadhaar",
                detail: "Enter a valid 12-digit Aadhaar number",
            });
            return;
        }
        setOtpLoading(true);
        try {
            const response = await generateOtp(aadhaarNumber);
            if (response?.status) {
                setShowOtpField(true);
                settxnId(response?.data?.txnId);
                toast?.show({
                    severity: "success",
                    summary: "Otp sent successfully",
                    detail: "Check your registered mobile number for OTP",
                });
                setAadhaarNumberSubmitButton(false);
            } else {
                toast?.show({
                    severity: "error",
                    summary: "Something went wrong",
                    detail: "Please try again later!",
                });
            }
        } catch (error) {
            console.error("Error during Aadhaar submission:", error);
            toast?.show({
                severity: "error",
                summary: "Recheck your aadhar number",
                detail: "Please try again later!",
            });
        } finally {
            setOtpLoading(false);
        }
    };
    const handleMobileNumberSubmit = async () => {
        if (!mobileNumber) {
            toast?.show({
                severity: "error",
                summary: "Invalid Mobile Number",
                detail: "Enter a valid 10-digit mobile number",
            });
            return;
        }
        setisOtpGenerateLoading(true);
        try {
            const response = await generateMobileOtp(mobileNumber, txnId);
            if (response?.status) {
                SetshowMobileOtpField(true);
                setAadhaarNumberSubmitButton(false);
                toast?.show({
                    severity: "success",
                    summary: "Otp sent successfully",
                    detail: "Check your registered mobile number for OTP",
                });
            } else {
                toast?.show({
                    severity: "error",
                    summary: "Something went wrong",
                    detail: "Please try again later!",
                });
            }
        } catch (error) {
            console.error("Error during mobile number submission:", error);
            toast?.show({
                severity: "error",
                summary: "Recheck your mobile number",
                detail: "Please try again later!",
            });
        } finally {
            setisOtpGenerateLoading(false);
        }
    };

    const handlecreateHPRID = async (token, hprIdNumber, hprId) => {
        try {
            if (!token || !hprIdNumber || !hprId) {
                const createHPIdResp = await createHPRID(
                    txnId,
                    user?.email,
                    user?.aadharPhotoUrl,
                    user?.firstname,
                    user?.textPassword,
                    user?.district_id,
                    user?.state_id,
                    selectedUsername
                );

                return createHPIdResp ? JSON.stringify(createHPIdResp) : "";
            }
            // return "";
        } catch (error) {
            console.error(error);
        }
    };

    const handleOtpSubmit = async (token, hprIdNumber, hprId) => {
        if (otp.length !== 6) {
            toast?.show({
                severity: "error",
                summary: "Invalid OTP",
                detail: "OTP must be 6 digits",
            });
            return;
        }
        setOtpVerifyLoading(true);
        try {
            const response = await verifyOtp(otp, txnId);

            const userVerifiedData = response?.data;

            if (response?.status) {
                const HPIDaccountExistResponse = await checkHPIDExist(txnId);
                sethprIdExist(HPIDaccountExistResponse?.data);
                const mobileNoResponse = await checkMobileNumberSameAsAAdhar(
                    txnId,
                    user?.mobile_number
                );

                const usersData = JSON.stringify(response?.data);
                const hpidData = JSON.stringify(HPIDaccountExistResponse?.data);
                const mobileResponseData = JSON.stringify(
                    mobileNoResponse?.data
                );

                if (mobileNoResponse?.isMobileNumberSame) {
                    toast?.show({
                        severity: "success",
                        summary: "Success",
                        detail: "OTP Verified Successfully",
                    });
                    setShowOtpField(false);
                    setAadhaarNumberSubmitButton(false);
                    setOtp("");
                    await fetchUserNameSuggestions(txnId);

                    const formattedBirthdate = formatDateForMySQL(
                        userVerifiedData?.birthdate
                    );
                    const usersResponse = await updateUsersData(userId, {
                        date_of_birth: formattedBirthdate,
                        permanent_address: userVerifiedData?.address,
                        firstname: userVerifiedData?.name,
                        gender: userVerifiedData?.gender,

                        aadharPhotoUrl: userVerifiedData?.photo,
                        usersData: usersData,
                        accountExistResponse: hpidData,
                        mobileandAadharMobileResponse: mobileResponseData,
                    });
                } else {
                    setShowMobileNumberField(true);
                    setShowOtpField(false);
                    toast?.show({
                        severity: "error",
                        summary: "Aadhaar Verification Failed",
                        detail: "The phone number linked to your Aadhaar does not match the one provided. Please update your phone number below",
                    });
                    // const generateMobileOtpResp = await generateMobileOtp();
                }
            } else {
                toast?.show({
                    severity: "error",
                    summary: "Verification Failed",
                    detail: "Please try again later!",
                });
            }
        } catch (error) {
            console.error("Error during OTP verification:", error);
            toast?.show({
                severity: "error",
                summary: "Verification Error",
                detail: "An error occurred. Please try again later!",
            });
        } finally {
            setOtpVerifyLoading(false);
        }
    };
    const handleMobileOtpSubmit = async () => {
        if (mobileOtp.length !== 6) {
            toast?.show({
                severity: "error",
                summary: "Invalid OTP",
                detail: "OTP must be 6 digits",
            });
            return;
        }
        setMobileOtpVerifyLoading(true);
        try {
            const response = await verifyMobileOtp(mobileOtp, txnId);

            if (response?.status) {
                const usersResponse = await updateUsersData(userId, {
                    mobile_number: mobileNumber,
                });
                toast?.show({
                    severity: "success",
                    summary: "Success",
                    detail: "Mobile Number updated Successfully",
                });
            } else {
                toast?.show({
                    severity: "error",
                    summary: "Verification Failed",
                    detail: "Please try again later!",
                });
            }
        } catch (error) {
            console.error("Error during OTP verification:", error);
            toast?.show({
                severity: "error",
                summary: "Verification Error",
                detail: "An error occurred. Please try again later!",
            });
        } finally {
            setOtpVerifyLoading(false);
        }
    };
    const handleUsernameChange = (e) => {
        setSelectedUsername(e.value);
    };

    const handleUserNameSubmit = async () => {
        SetLoadingUserNameSubmit(true);
        if (!selectedUsername) {
            toast?.show({
                severity: "warn",
                summary: "Validation Error",
                detail: "Please select a username before submitting!",
            });
            return;
        }
        try {
            const usersResponse = await updateUsersData(userId, {
                aadharVerified: "yes",
            });
            if (usersResponse) {
                const createHPIdRespData = await handlecreateHPRID(
                    hprIdExist.token,
                    hprIdExist.hprIdNumber,
                    hprIdExist.hprId
                );
                if (createHPIdRespData) {
                    const updateResponse = await updateUsersData(userId, {
                        userName: selectedUsername,
                        createHPRIDResponse: createHPIdRespData,
                    });
                }
                toast?.show({
                    severity: "success",
                    summary: "Success",
                    detail: "Username submitted successfully!",
                });
                setshowUserNameSubmit(false);
                setTimeout(() => {
                    setActiveIndex(1);
                    window.location.reload();
                }, 3000);
            }
        } catch (error) {
            console.error("Failed to submit the user name", error);
            toast?.show({
                severity: "error",
                summary: "Submission Error",
                detail: "An error occurred. Please try again later!",
            });
        } finally {
            SetLoadingUserNameSubmit(false);
        }
    };

    return (
        <div className="center-align">
            <Toast ref={(el) => setToast(el)} />
            <Card title="Aadhaar Verification" style={{width:"40%"}}>
                <div
                    className="p-field"
                    style={{
                        paddingBottom: "10px",
                        display: "flex",
                        flexDirection: "column",
                        gap: "10px",
                    }}
                >
                    <div className="grid">
                        <div className="col-12 md:col-12 lg:col content">
                            <div>
                                <span className="p-float-label">
                                    <InputText
                                        className="vw-input"
                                        autoComplete="off"
                                        value={aadhaarNumber}
                                        onChange={(e) =>
                                            setAadhaarNumber(e.target.value)
                                        }
                                        maxLength={12}
                                        disabled={showOtpField}
                                    />
                                    <label>Aadhaar Number. *</label>
                                </span>
                            </div>
                        </div>

                        {/* <label style={{ paddingRight: "10px" }}></label>
            
            <InputText
              maxLength={12}
              disabled={showOtpField}
              placeholder="Enter Aadhaar Number"
              style={{ paddingLeft: "10px" }}
            /> */}
                    </div>

                    {showOtpField && (
                        <div>
                            <span className="p-float-label">
                                <InputText
                                    className="vw-input"
                                    autoComplete="off"
                                    value={otp}
                                    onChange={(e) => setOtp(e.target.value)}
                                    maxLength={6}
                                    // placeholder="Enter OTP"
                                />
                                <label style={{ paddingRight: "10px" }}>
                                    Enter OTP
                                </label>
                            </span>
                        </div>
                    )}
                    {showMobileNumberField && (
                        <div>
                            <span className="p-float-label">
                                <InputText
                                    className="vw-input"
                                    value={mobileNumber}
                                    onChange={(e) =>
                                        SetMobileNumber(e.target.value)
                                    }
                                    maxLength={10}
                                />
                                <label style={{ paddingRight: "10px" }}>
                                    Enter Mobile number registered with Aadhar
                                </label>
                            </span>
                        </div>
                    )}
                    {showMobileOtpField && (
                        <div>
                            <span className="p-float-label">
                                <InputText
                                    value={mobileOtp}
                                    onChange={(e) =>
                                        setMobileOtp(e.target.value)
                                    }
                                    maxLength={6}
                                    className="vw-input"
                                />
                                <label style={{ paddingRight: "10px" }}>
                                    Enter OTP
                                </label>
                            </span>
                        </div>
                    )}

                    {showUserNameSubmit && (
                        <div>
                            <span className="p-float-label">
                                <Dropdown
                                    value={selectedUsername}
                                    options={usernameOptions?.map((u) => ({
                                        label: u,
                                        value: u,
                                    }))}
                                    onChange={handleUsernameChange}
                                    className="vw-dropdown"
                                    style={{ width: "100%" }}
                                />
                                <label>Select Username *</label>
                            </span>
                        </div>
                    )}
                </div>

                {aadhaarNumberSubmitButton && (
                    <Button
                        label="Submit"
                        onClick={handleAadhaarSubmit}
                        className="p-mt-2"
                        style={{ margin: "10px" }}
                        loading={isOtpLoading}
                        disabled={showMobileNumberField}
                    />
                )}

                {showOtpField && (
                    <Button
                        label="Verify OTP"
                        onClick={handleOtpSubmit}
                        className="p-mt-2"
                        style={{ margin: "10px" }}
                        loading={isOtpVerifyLoading}
                    />
                )}
                {showMobileNumberField && (
                    <Button
                        label="Send Otp"
                        onClick={handleMobileNumberSubmit}
                        className="p-mt-2"
                        style={{ margin: "10px" }}
                        loading={isOtpGenerateLoading}
                        disabled={showMobileOtpField}
                    />
                )}
                {showMobileOtpField && (
                    <Button
                        label="Verify Mobile OTP"
                        onClick={handleMobileOtpSubmit}
                        className="p-mt-2"
                        style={{ margin: "10px" }}
                        loading={isMobileOtpVerifyLoading}
                    />
                )}
                {showUserNameSubmit && (
                    <Button
                        loading={loadingUserNameSubmit}
                        disabled={loadingUserNameSubmit}
                        label="Submit username"
                        onClick={handleUserNameSubmit}
                        className="p-mt-2"
                        style={{ margin: "10px" }}
                    />
                )}
            </Card>
        </div>
    );
};

export default AadhaarVerification;
