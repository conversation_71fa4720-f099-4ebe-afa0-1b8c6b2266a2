import RestClient from "RestClient";
const BASE_URL = process.env.REACT_APP_API_BASE_URL;
const restClient = new RestClient();

export const generateOtp = async (aadharNumber) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/generateOtp`,
    {
      aadharNumber,
    }
  );
  return result?.data;
};
export const checkHPIDExist = async (txnId) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/checkHPIDExist`,
    {
      txnId,
    }
  );
  return result?.data;
};
export const checkMobileNumberSameAsAAdhar = async (txnId, mobileNumber) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/checkMobileNumberSameAsAAdhar`,
    {
      txnId: txnId,
      mobileNumber: mobileNumber,
    }
  );
  return result?.data;
};
export const verifyOtp = async (otp, txnId) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/verifyOtp`,
    {
      otp,
      txnId,
    }
  );
  return result?.data;
};
export const verifyMobileOtp = async (otp, txnId) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/verifyMobileOtp`,
    {
      otp,
      txnId,
    }
  );
  return result?.data;
};
export const generateMobileOtp = async (mobile, txnId) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/generateMobileOtp`,
    {
      mobile: mobile,
      txnId: txnId,
    }
  );
  return result?.data;
};
export const getuserNameSuggestions = async (txnId) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/getuserNameSuggestions`,
    {
      txnId: txnId,
    }
  );
  return result?.data?.data;
};
export const createHPRID = async (
  txnId,
  email,
  profilePhoto,
  firstName,
  password,
  district_id,
  state_id,
  userName
) => {
  const result = await restClient.post(
    `${BASE_URL}/aadhar-verfication/createHPRID`,
    {
      txnId: txnId,
      email: email,
      profilePhoto: profilePhoto,
      firstName: firstName,
      password: password,
      district_id: district_id,
      state_id: state_id,
      hprId: userName,
    }
  );
  return result?.data?.data;
};

export const updateUsersData = async (id, params) => {
  const result = await restClient.put(`${BASE_URL}/users/${id}`, params);
  return result?.data;
};
export const getUserData = async (id) => {
  const result = await restClient.get(`${BASE_URL}/users/${id}`, []);
  return result?.data;
};
