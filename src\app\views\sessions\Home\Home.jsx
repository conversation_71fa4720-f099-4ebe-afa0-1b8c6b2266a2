import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Tab<PERSON>iew } from "primereact/tabview";
import MyProfile from "../my-profile/MyProfile";
import MyDocumentList from "../my-documents/MyDocumentList";
import Certificate from "../Certificate/Certificate";
import { Fragment, useEffect, useState } from "react";
import { getUserDocumentList } from "../my-documents/my-document.service";
import { getSessionUserInformation } from "app/utils/utility.service";
import { getSingleUser, updateUser } from "../my-profile/my-profile.service";
import Payments from "../payments/payments";
import { Message } from "primereact/message";
import { Checkbox } from "primereact/checkbox";
import { Button } from "primereact/button";
import { Loading } from "app/components";
import AadhaarVerification from "../aadhar-verification/AadharVerification";
import Professionalnformation from "../professional-information/Professional-Information";
import ResetPassword from "../reset-password/reset-password";

const Home = () => {
  const sessionValues = getSessionUserInformation();
  const [disableCertificate, setDisableCertificate] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [checkChanges, setCheckChanges] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [checked, setChecked] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const fetchUserDocumentList = async () => {
    // setIsLoading(true);

    let list = await getUserDocumentList(sessionValues?.id);
    if (list?.length > 0) {
      const record = list.find(
        (li) => li.is_mandatory === "Yes" && li.fileName === null
      );
      if (record) {
        setDisableCertificate(true);
      } else {
        setDisableCertificate(false);
      }
      return;
    } else {
      setDisableCertificate(false);
    }
    return;
  };

  const fetchUser = async () => {
    setIsLoading(true);
    let record = await getSingleUser(sessionValues.id);
    if (record?.profile_status === "Approved") {
      setActiveIndex(4);
    }
    setCurrentRecord(record);
    setChecked(record?.declaration === "yes" ? true : false);
    setIsLoading(false);
  };

  const doValidationCheckForPayment = () => {
    let flag = false;
    if (disableCertificate) {
      flag = true;
    } else if (!currentRecord?.declaration) {
      flag = true;
    }
    return flag;
  };

  const doValidationCheckForCertificate = () => {
    let flag = true;
    // if (currentRecord && currentRecord?.profileImagePath) {
    //     flag = false;
    // } else {
    //     flag = true;
    // }
    if (currentRecord && currentRecord.profile_status === "Approved") {
      flag = false;
    } else {
      flag = true;
    }
    return flag;
  };

  useEffect(() => {
    if (!!sessionValues.id) {
      fetchUser();
      fetchUserDocumentList();
    }
  }, [sessionValues.id, checkChanges]);

  //if aadhar is disabled move to profile section
  useEffect(() => {
    if (currentRecord?.aadharVerified === "yes") {
      setActiveIndex(1);
    }
  }, [currentRecord?.aadharVerified]);

  const handleSubmit = async () => {
    const userInfo = await getSingleUser(sessionValues?.id);
    const payload = { ...userInfo };
    payload.declaration = checked ? "yes" : "no";
    const record = await updateUser(payload.id, payload);
    if (record?.status === false) {
      setIsLoading(false);
      alert(record?.message);
    } else {
      alert("Declaration updated successfully");
      // insert settings value
      fetchUser();
      setIsLoading(false);
    }
  };
  const declaration = () => {
    return (
      <Fragment>
        <h3>
          <Checkbox
            disabled={currentRecord?.declaration === "yes"}
            className="mr-4"
            name="declaration"
            value="yes"
            onChange={(e) => {
              setChecked(e.checked);
            }}
            checked={checked}
          />
          I hereby declare that the information furnished in this Application is
          correct and true to the best of my knowledge and belief. I understand
          that furnishing false/improper information will lead to rejection and
          cancellation of my candidature. I am liable for legal and/or
          disciplinary action as may be initiated by the Naturopathy
          Registration Board.
          <br />
          If at any stage, the information given by the applicant is found to be
          fake or wrong, the registration will be cancelled.
        </h3>
        <Button
          onClick={handleSubmit}
          label="Save"
          className="p-button-raised p-button-warning p-button-lg mr-4"
          disabled={!checked}
        />
      </Fragment>
    );
  };

  const profileStatusMessage = () => {
    return (
      <Fragment>
        {sessionValues?.profile_status && (
          <label style={{ color: "#fff" }}>
            Your Profile Status is {currentRecord?.profile_status}
          </label>
        )}
        {!sessionValues?.profile_status && (
          <label style={{ color: "#fff" }}>
            Your Profile Status is In Progress
          </label>
        )}
      </Fragment>
    );
  };
  return (
    <Fragment>
      {sessionValues?.profile_status && (
        <div className="card flex flex-wrap align-items-center gap-3 mb-5">
          <Message
            className="message_info"
            severity="error"
            text={profileStatusMessage()}
          />
        </div>
      )}
      {isLoading && <Loading />}
      <TabView
        activeIndex={activeIndex}
        onTabChange={(e) => setActiveIndex(e.index)}
      >
        <TabPanel
          header="Aadhar Verification"
          disabled={currentRecord?.aadharVerified == "yes"}
        >
          <AadhaarVerification
            userId={sessionValues?.id}
            setActiveIndex={setActiveIndex}
          />
        </TabPanel>
        <TabPanel
          header="Professional Information"
          disabled={
            currentRecord?.profile_status === "Approved" ||
            currentRecord?.aadharVerified == "no"
          }
        >
          <Professionalnformation userId={sessionValues?.id} />
        </TabPanel>

        <TabPanel
          disabled={
            currentRecord?.profile_status === "Approved" ||
            currentRecord?.aadharVerified == "no"
          }
          header="Profile"
        >
          <MyProfile fetchUser={fetchUser} />
        </TabPanel>
        <TabPanel
          header="Documents"
          disabled={
            currentRecord?.profile_status === "Approved" ||
            currentRecord?.aadharVerified == "no"
          }
        >
          <MyDocumentList
            checkChanges={checkChanges}
            setCheckChanges={setCheckChanges}
          />
        </TabPanel>
        <TabPanel
          header="Declaration"
          disabled={
            currentRecord?.profile_status === "Approved" ||
            currentRecord?.aadharVerified == "no"
          }
        >
          {declaration()}
        </TabPanel>
        {sessionValues?.certificate_status === "New Certificate" && (
          <TabPanel
            header="Proceed For Payment"
            disabled={
              doValidationCheckForPayment() ||
              currentRecord?.aadharVerified == "no"
            }
          >
            <Payments
              currentUserRecord={currentRecord}
              checkChanges={checkChanges}
              setCheckChanges={setCheckChanges}
            />
            {/* <MyDocumentList checkChanges={checkChanges} setCheckChanges={setCheckChanges} /> */}
          </TabPanel>
        )}
        <TabPanel
          disabled={
            doValidationCheckForCertificate() ||
            currentRecord?.aadharVerified == "no"
          }
          header="Certificate"
        >
          <Certificate />
        </TabPanel>
      </TabView>
    </Fragment>
  );
};

export default Home;
