import { Loading } from "app/components";
import moment from "moment";
import { <PERSON><PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Panel } from "primereact/panel";
import { Toast } from "primereact/toast";
import { Fragment, useEffect, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { getQRInfo } from "./qrverification.service";
import { SuccessToastConfig } from "app/utils/ToastConstants";
import { ErrorToastConfig } from "app/utils/ToastConstants";

const QRVerification = () => {
    const toast = useRef(null);
    const [isLoading, setIsLoading] = useState(false);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [userInfo, setUserInfo] = useState(null);
    const [searchParams] = useSearchParams();
    const qrCode = searchParams.get("qrCode");

    const fetchQRInfo = async () => {
        setIsLoading(true);

        const qrInfo = await getQRInfo({ qrCode: qrCode });
        setIsLoading(false);
        if (!qrInfo) {
            setShowErrorMessage(true);
            // showError("This is not a valid certificate");
            return;
        } else {
            setShowErrorMessage(false);
            setUserInfo(qrInfo);
            // showSuccess("This is a valid certificate");
        }
    };

    const showSuccess = (message) => {
        toast.current.show(SuccessToastConfig(message));
    };
    const showError = (message) => {
        toast.current.show(ErrorToastConfig(message ? message : "Error"));
    };

    useEffect(() => {
        fetchQRInfo();
    }, []);

    return (
        <Fragment>
            <div className="card" style={{ width: "100%", margin: "auto" }}>
                <Card
                    className="mb-4"
                    title={
                        userInfo?.firstname
                            ? "This is a valid Naturopathy Registration Certificate issued to"
                            : "Details provided are invalid"
                    }
                >
                    <Toast ref={toast} className="ToastMessage" />
                    {isLoading && (
                        <div className="spinner inner-spinner">
                            <Loading />
                        </div>
                    )}
                    {userInfo?.firstname && (
                        <Fragment>
                            <div className="grid">
                                <div className="col-9">
                                <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Name</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>Dr. {userInfo?.firstname}</strong>
                                </div>
                            </div>
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>DOB</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>
                                        {moment(userInfo?.date_of_birth).format(
                                            "DD/MMM/YYYY"
                                        )}
                                    </strong>
                                </div>
                            </div>
                            
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Registration No.</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>
                                        {userInfo?.central_registration_number}
                                    </strong>
                                </div>
                            </div>
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Issue Date</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>
                                        {moment(userInfo?.approved_date).format(
                                            "DD/MMM/YYYY"
                                        )}
                                    </strong>
                                </div>
                            </div>
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Valid Upto</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>
                                        {moment(userInfo?.approved_date).add(5 ,'years').format(
                                            "MMM/YYYY"
                                        )}
                                    </strong>
                                </div>
                            </div>
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Qualification</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>{userInfo?.qualification}</strong>
                                </div>
                            </div>
                            <div className="grid">
                                <div className="col-4">
                                    {" "}
                                    <label>
                                        <strong>Permanent Address</strong>
                                    </label>
                                </div>

                                <div className="col-8">
                                    <strong>
                                        {userInfo?.permanent_address}
                                    </strong>
                                </div>
                            </div>
                                </div>
                                <div className="col-3">
                                    <div style={{margin: "auto"}} className="mb-4">
                                        {" "}
                                            <img style={{width:"100px"}} src={`${process.env.REACT_APP_API_BASE_URL}/fetchDocumentsPDF/${userInfo?.documentInfo?.fileName}`} />
                                    </div>
                                </div>
                            </div>
                            
                            
                        </Fragment>
                    )}
                </Card>
            </div>
        </Fragment>
    );
};

export default QRVerification;
