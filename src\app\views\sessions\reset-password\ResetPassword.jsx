import { Loading } from "app/components";
import { ErrorToastConfig } from "app/utils/ToastConstants";
import { SuccessToastConfig } from "app/utils/ToastConstants";
import { Button } from "primereact/button";
import { Password } from "primereact/password";
import { Toast } from "primereact/toast";
import { useRef, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { doResetPassword } from "../session.service";

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const email = searchParams.get("email");
  const token = searchParams.get("token");
  const [password, setPassword] = useState(null);
  const [confirmPassword, setConfirmPassword] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const toast = useRef();

  const showSuccess = (message) => {
    toast.current.show(SuccessToastConfig(message));
  };

  const showError = (message) => {
    toast.current.show(ErrorToastConfig(message ? message : "Error"));
  };

  const validateInput = () => {
    if (!password) {
      showError("Password is required.");
      return false;
    }
    if (!confirmPassword) {
      showError("Confirm Password is required.");
      return false;
    }

    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[\W_]/.test(password);
    const noConsecutiveAlpha = !/(?:([a-zA-Z])\1)/.test(password);
    const noConsecutiveNumbers = !/(?:([0-9])\1)/.test(password);

    if (!hasUpperCase) {
      showError("Password must contain at least one uppercase letter.");
      return false;
    }
    if (!hasLowerCase) {
      showError("Password must contain at least one lowercase letter.");
      return false;
    }
    if (!hasNumber) {
      showError("Password must contain at least one number.");
      return false;
    }
    if (!hasSpecialChar) {
      showError("Password must contain at least one special character.");
      return false;
    }
    if (!noConsecutiveAlpha) {
      showError("Password cannot have consecutive identical letters.");
      return false;
    }
    if (!noConsecutiveNumbers) {
      showError("Password cannot have consecutive identical numbers.");
      return false;
    }
    if (password !== confirmPassword) {
      showError("Password and Confirm Password do not match.");
      return false;
    }

    return true;
  };

  const handleClick = async (e) => {
    e.preventDefault();
    if (!validateInput()) {
      return false;
    }

    const payload = {
      email,
      token,
      password,
      confirmPassword,
    };
    setIsLoading(true);

    const record = await doResetPassword(payload);
    if (!record.status) {
      setIsLoading(false);
      showError(record?.message);
    } else {
      setIsLoading(false);
      // setFormSubmitted(false);
      showSuccess("New password set successfully");
      setTimeout(() => {
        navigate("/session/signin/step1");
      }, 2000);
      return;
    }
  };

  return (
    <div className="center-align">
      {isLoading && <Loading />}
      <Toast ref={toast} className="ToastMessage" />
      <div className="LoginForm ForgotPasswordForm">
        <h2>Reset Password</h2>
        <form onSubmit={handleClick} method="POST">
          <div className="content">
            <div className="email-input mb-4">
              <span className="p-float-label">
                <Password
                  type="password"
                  autoFocus
                  className="vw-input-password"
                  autoComplete="off"
                  required={true}
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  feedback={false}
                  toggleMask
                />
                <label>Password *</label>
              </span>
              {/* <div>
                                {formSubmitted && !password && (
                                    <small className="p-error">
                                        Please enter password.
                                    </small>
                                )}
                            </div> */}
            </div>
            <div className="email-input">
              <span className="p-float-label">
                <Password
                  type="password"
                  className="vw-input-password"
                  autoComplete="off"
                  required={true}
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  feedback={false}
                  toggleMask
                />
                <label>Confirm Password *</label>
              </span>
            </div>
            <div className="mt-4" style={{ marginLeft: "13rem" }}>
              <Button
                label="Cancel"
                type="button"
                className="p-button-raised p-button-lg login-button mr-2"
                onClick={() => navigate("/session/signin/step1")}
              />
              <Button
                disabled={
                  !password ||
                  password === "" ||
                  !confirmPassword ||
                  !confirmPassword
                }
                label="Reset Password"
                className="p-button-raised p-button-warning p-button-lg login-button"
                onClick={handleClick}
              />
            </div>
          </div>
        </form>
      </div>
      {/* <div>Step 1</div>
            <Button
                onClick={handleClick}
                label="Step 2"
                className="p-button-raised p-button-warning p-button-lg"
            /> */}
    </div>
  );
};

export default ResetPassword;
