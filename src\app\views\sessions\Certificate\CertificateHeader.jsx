import moment from "moment";
import { Fragment } from "react";
import Barcode from 'react-barcode';

const CertificateHeader = ({userValues}) => {
    return (
        <Fragment>
            <div className="grid">
                <div className="col-6">
                    <div style={{width:"100%"}}>
                    {/* <Barcode value={`https://app.nrb.net.in/verifyBarcode/${userValues?.central_registration_number_pre}-${userValues?.central_registration_number}`} displayValue={false} width={0.3} height={70} marginTop={-40} /> */}
                    </div>
                </div>
                <div className="col-6 text-right">
                    <label className="" style={{ color: "#040000" }}>
                        Certificate Number: <strong>{`${userValues?.central_registration_number_pre}${userValues?.central_registration_number}`}</strong>
                    </label>
                </div>
            </div>
            <div className="grid">
                <div className="col-2">
                    <div className="flex justify-content-center">
                        <img style={{height:"150px"}} src="/ministry_of_ayush-removebg.png" alt="NRB Logo" />
                    </div>
                </div>
                <div className="col-8 text-center">

                    <div className="grid">
                        <div className="col-12">
                            <h2 className="text-center mb-0" style={{ color: "#040000" }}>
                                NATUROPATHY REGISTRATION BOARD 
                            </h2>
                            <h3 className="text-center mb-0 mt-0" style={{ color: "#040000" }}>
                                (Ministry of Ayush, Govt. of India)
                            </h3>
                            <label>Bapu Bhavan, Matoshree Ramabai Ambedkar Rd, Pune, <br />Maharashtra 411001</label>
                        </div>
                        <div className="col-12">
                            <h2 className="text-center mb-0 mt-0" style={{ color: "#040000" }}>
                            प्राकृतिक चिकित्सा पंजीकरण बोर्ड
                            </h2>
                            <h3 className="text-center mb-0 mt-0" style={{ color: "#040000" }}>
                            (आयुष मंत्रालय, भारत सरकार)
                            </h3>

                            <label>बापू भवन, मातोश्री रमाबाई आंबेडकर रोड, पुणे, महाराष्ट्र 411001</label>
                        </div>
                    <div className="col-12">
                        <h2 className="text-center mb-0 mt-0" style={{ color: "#040000" }}>
                            CENTRAL REGISTRATION CERTIFICATE
                        </h2>
                        <h3 className="text-center mt-0" style={{ color: "#040000" }}>
                            केंद्रीय पंजीकरण प्रमाण पत्र
                        </h3>
                </div>
                    </div>
                </div>
                <div className="col-2">
                    <div className="flex justify-content-center">
                            <img style={{height:"150px"}} src="/nrblogo-removebg.png" alt="NRB Logo" />
                        </div>
                </div>
                
            </div>
             
        </Fragment>
    );
};

export default CertificateHeader;
